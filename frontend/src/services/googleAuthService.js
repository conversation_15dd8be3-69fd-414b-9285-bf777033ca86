import api from './api';

export const googleLogin = async (googleToken) => {
  try {

    
    // Use our API service instance which already has withCredentials: true
    const response = await api.post(`/auth/google`, {
      token: googleToken,
      tokenType: 'access_token'
    });
    

    
    // Backend appears to be setting auth via cookie, so just check for user data
    if (response.data && response.data.user) {
      // Return what we have - the cookie should handle authentication
      return {
        user: response.data.user,
        token: 'cookie-based-auth' // Use placeholder since we're using cookies
      };
    } else {
      // Provide more detailed error about what's missing
      const missingFields = [];
      if (!response.data) missingFields.push('response.data');
      else {
        if (!response.data.user) missingFields.push('user');
      }
      
      throw new Error(`Invalid response from server: Missing ${missingFields.join(', ')}`);
    }
  } catch (error) {
    console.error('Google login error:', error);
    
    // More detailed error logging
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
    } 
    
    // Pass through any server-provided error message or use our more specific one
    throw error.response?.data?.message || 
          (error.message?.includes('Invalid response') ? error.message : 'Authentication failed');
  }
};



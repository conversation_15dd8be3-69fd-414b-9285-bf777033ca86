import axios from 'axios';

import { API_URL } from '../config/env.js';

// Create an instance of axios with baseURL
const uploadApi = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Use cookies for authentication
  headers: {
    'Content-Type': 'multipart/form-data' // For file uploads
  }
});

// Remove auth token interceptor since we're using cookies
uploadApi.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => Promise.reject(error)
);

// Enhanced upload function with multi-phase progress tracking
export const uploadGame = async (
  gameData,
  gameFile, // Single game file
  coverImage, // Not used anymore but kept for compatibility
  cardImage,
  gifAnimation,
  screenshots, // Not used anymore but kept for compatibility
  onProgress = null // Progress callback function
) => {
  try {
    // Calculate total file sizes for accurate progress tracking
    const totalFiles = [gameFile, cardImage, gifAnimation].filter(Boolean);
    const totalSize = totalFiles.reduce((sum, file) => sum + file.size, 0);

    // Progress phases:
    // Phase 1: HTTP Upload (0-70%)
    // Phase 2: Server Processing (70-100%)

    // Create FormData object to handle file uploads
    const formData = new FormData();

    // Add game metadata as JSON string
    formData.append('gameData', JSON.stringify(gameData));

    // Handle single game file
    if (gameFile) {
      formData.append('gameFile', gameFile);
    }

    // Add other files (only card image and GIF now)
    if (cardImage) {
      formData.append('cardImage', cardImage);
    }

    if (gifAnimation) {
      formData.append('gifAnimation', gifAnimation);
    }

    // Make API call to upload the game with enhanced progress tracking
    const response = await axios.post(`${API_URL}/upload/game`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      withCredentials: true, // Use cookies for authentication
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          // HTTP upload represents 70% of total progress
          const uploadPercent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          const totalProgress = Math.round(uploadPercent * 0.7);
          console.log(`Upload progress: ${uploadPercent}% (HTTP) -> ${totalProgress}% (Total)`);
          onProgress(totalProgress);
        }
      }
    });

    // Phase 2: Server processing simulation (70-100%)
    // Since we can't get real-time server progress, simulate based on file types and sizes
    if (onProgress) {
      const processingSteps = [];
      const processingFiles = [];

      // Add processing steps based on uploaded files
      if (gameFile) {
        processingSteps.push('Processing game file...');
        processingFiles.push(gameFile);
      }
      if (cardImage) {
        processingSteps.push('Processing card image...');
        processingFiles.push(cardImage);
      }
      if (gifAnimation) {
        processingSteps.push('Processing hover animation...');
        processingFiles.push(gifAnimation);
      }

      const stepIncrement = 30 / processingSteps.length; // Remaining 30% divided by steps

      for (let i = 0; i < processingSteps.length; i++) {
        const currentFile = processingFiles[i];

        // Calculate processing time based on file size and type
        let processingTime;
        if (currentFile === gameFile) {
          // Game files take longer due to ZIP extraction
          processingTime = Math.min(Math.max(currentFile.size / (1024 * 1024) * 200, 500), 2000); // 200ms per MB, min 500ms, max 2s
        } else if (currentFile === cardImage) {
          // Card images need processing (resize to 230x130)
          processingTime = Math.min(Math.max(currentFile.size / (1024 * 1024) * 150, 300), 1000); // 150ms per MB, min 300ms, max 1s
        } else {
          // GIF animations are uploaded as-is
          processingTime = Math.min(Math.max(currentFile.size / (1024 * 1024) * 100, 200), 800); // 100ms per MB, min 200ms, max 800ms
        }

        // Simulate gradual progress within each step
        const stepStartProgress = 70 + Math.round(i * stepIncrement);
        const stepEndProgress = 70 + Math.round((i + 1) * stepIncrement);
        const subSteps = 5; // Break each step into 5 sub-steps for smoother progress

        for (let j = 0; j < subSteps; j++) {
          await new Promise(resolve => setTimeout(resolve, processingTime / subSteps));
          const subProgress = stepStartProgress + Math.round((j + 1) * (stepEndProgress - stepStartProgress) / subSteps);
          console.log(`Processing step ${i + 1}/${processingSteps.length}, sub-step ${j + 1}/${subSteps}: ${subProgress}%`);
          onProgress(Math.min(subProgress, 100));
        }
      }

      // Final step to ensure we reach 100%
      onProgress(100);
    }

    return response.data;
  } catch (error) {
    console.error('Error uploading game:', error);
    throw error.response?.data || { message: 'Network error during upload' };
  }
};

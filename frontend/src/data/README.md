# Game Categories System

This directory contains the centralized game categories configuration for the IndieRepo project.

## Files

### `gameCategories.json`
Contains the master list of all game categories used throughout the application. Each category has:
- `value`: The internal value used in forms and database (e.g., "action")
- `label`: The display name shown to users (e.g., "Action")
- `slug`: The URL-friendly version used in routes (e.g., "action")

## Usage

### Adding New Categories
To add a new category, simply add it to the `categories` array in `gameCategories.json`:

```json
{
  "value": "newcategory",
  "label": "New Category",
  "slug": "newcategory"
}
```

The system will automatically:
- Add the category to all dropdown menus
- Create the appropriate routes
- Include it in category filtering

### Using Categories in Components

Import the utility functions from `../utils/categoryUtils.js`:

```javascript
import { 
  getGameCategories, 
  getCategoryOptions, 
  getCategoryByValue,
  isValidCategoryValue 
} from '../utils/categoryUtils';

// Get all categories
const categories = getGameCategories();

// Get options for select dropdowns
const options = getCategoryOptions();

// Validate a category
const isValid = isValidCategoryValue('action');
```

### Current Categories
The system currently includes these categories:
- Action
- Adventure
- RPG
- Simulation
- Strategy
- Sports
- Puzzle
- Horror
- Platformer
- Shooter

## Benefits

1. **Centralized Management**: All categories are defined in one place
2. **Consistency**: Same categories used across all pages and components
3. **Easy Maintenance**: Add/remove categories by editing one JSON file
4. **Type Safety**: Utility functions provide validation and type checking
5. **Automatic Integration**: New categories automatically appear in all relevant places
6. **Clean Implementation**: Only JSON categories are used, no legacy hardcoded categories
7. **Multi-language Support**: All categories have proper translations

## Files Using This System

- `pages/UploadGamePage.jsx` - Genre selection dropdown
- `pages/HomePage.jsx` - Category sections
- `pages/CategoryPage.jsx` - Category filtering and routing
- `components/Sidebar.jsx` - Category navigation items
- `App.jsx` - Dynamic route generation
- `utils/categoryUtils.js` - Utility functions for category management
- `translations/*.json` - Category translations for i18n support

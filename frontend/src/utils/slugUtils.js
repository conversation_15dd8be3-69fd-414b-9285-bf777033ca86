/**
 * Generate SEO-friendly slug from text
 * @param {string} text - The text to convert to slug
 * @returns {string} - SEO-friendly slug
 */
export const generateSlug = (text) => {
  if (!text || typeof text !== 'string') return '';

  const slug = text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .trim() // Remove leading/trailing spaces
    .substring(0, 100); // Limit length

  // If slug is empty after processing, return a fallback
  return slug || 'untitled-game';
};

/**
 * Get game slug from game object
 * @param {Object} game - Game object
 * @returns {string} - Game slug
 */
export const getGameSlug = (game) => {
  if (!game || typeof game !== 'object') return 'untitled-game';

  // Use existing slug if available and valid
  if (game.slug && typeof game.slug === 'string' && game.slug.trim()) {
    return game.slug.trim();
  }

  // Generate slug from title, with fallback for empty/invalid titles
  const generatedSlug = generateSlug(game.title);
  return generatedSlug || 'untitled-game';
};

/**
 * Validate if a slug is properly formatted
 * @param {string} slug - Slug to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export const isValidSlug = (slug) => {
  if (!slug || typeof slug !== 'string') return false;

  // Check if slug matches expected format
  const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugPattern.test(slug) && slug.length <= 100;
};

export default {
  generateSlug,
  getGameSlug,
  isValidSlug
};

/**
 * Search utility functions for filtering and matching games
 */

/**
 * Check if text starts with search term (word boundary matching)
 * @param {string} text - Text to search in
 * @param {string} searchTerm - Term to search for
 * @returns {boolean} - True if text starts with search term or any word starts with search term
 */
export const startsWithTerm = (text, searchTerm) => {
  if (!text || !searchTerm) return false;
  
  const lowerText = text.toLowerCase();
  const lowerSearchTerm = searchTerm.toLowerCase();

  // Check if it starts with the search term
  if (lowerText.startsWith(lowerSearchTerm)) return true;

  // Check if any word starts with the search term
  const words = lowerText.split(/\s+/);
  return words.some(word => word.startsWith(lowerSearchTerm));
};

/**
 * Filter games based on strict word-boundary search
 * @param {Array} games - Array of games to filter
 * @param {string} searchTerm - Search term
 * @returns {Array} - Filtered and sorted games
 */
export const filterGamesBySearch = (games, searchTerm) => {
  if (!searchTerm.trim() || !games.length) return games;

  const filtered = games.filter(game => {
    // Only search by game title from starting characters
    // This prevents games with matching genres/tags from showing in search results
    return startsWithTerm(game.title, searchTerm);
  });

  // Sort results alphabetically by title since we're only matching titles
  return filtered.sort((a, b) => a.title.localeCompare(b.title));
};

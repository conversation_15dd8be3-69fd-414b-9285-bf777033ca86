/**
 * Helper function to get authentication configuration for API requests
 * Returns configuration for cookie-based authentication
 */
export const getAuthConfig = () => {
  return {
    credentials: 'include', // Use cookies for authentication
    headers: {
      'Content-Type': 'application/json'
    }
  };
};

/**
 * @deprecated Use getAuthConfig() instead. This function is kept for backward compatibility.
 * Helper function to get authentication headers for API requests
 * Returns headers with auth token if available
 */
export const getAuthHeader = () => {
  console.warn('getAuthHeader() is deprecated. Use getAuthConfig() for cookie-based authentication.');
  return getAuthConfig();
};

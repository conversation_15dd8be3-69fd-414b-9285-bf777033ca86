import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PropTypes from 'prop-types';
import { AuthProvider } from './context/AuthContext';
import { LanguageProvider } from './context/LanguageContext';
import { SidebarProvider } from './context/SidebarContext';
import { NotificationProvider } from './context/NotificationContext';
import { GoogleOAuthProvider } from '@react-oauth/google';
import LanguageRouter from './components/LanguageRouter';
import LanguageURLHandler from './components/LanguageURLHandler';
import DiscordCallback from './pages/DiscordCallback';
import AdminLayout from './pages/admin/AdminLayout';
import AdminOverview from './pages/admin/AdminOverview';
import AdminUsers from './pages/admin/AdminUsers';
import AdminGames from './pages/admin/AdminGames';
import AdminTickets from './pages/admin/AdminTickets';
import AdminReports from './pages/admin/AdminReports';
import AdminReviews from './pages/admin/AdminReviews';
import AdminLogs from './pages/admin/AdminLogs';
import AdminSettings from './pages/admin/AdminSettings';
import NotificationContainer from './components/NotificationContainer';

import Header from './components/Header';
import Footer from './components/Footer';
import Sidebar from './components/Sidebar';
import { useSidebar } from './context/SidebarContext';
import { useLayoutConfig } from './hooks/useLayoutConfig';

function AppContent({ children }) {
  const { isSidebarOpen, toggleSidebar } = useSidebar();
  const { shouldShowSidebar, shouldShowHeader, shouldShowFooter } = useLayoutConfig();

  // Admin routes get minimal layout (no header, no footer, no sidebar)
  if (!shouldShowHeader && !shouldShowFooter) {
    return (
      <div className="min-h-screen bg-[#0d0d0d] text-white">
        {children}
        <NotificationContainer />
      </div>
    );
  }

  // Regular layout - conditionally show sidebar, but always show header/footer for non-admin routes
  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white">
      {shouldShowHeader && <Header />}
      <div className="flex flex-1">
        {shouldShowSidebar && <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />}
        <main className={`flex-1 ${shouldShowSidebar && isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} overflow-x-hidden`}>
          {children}
        </main>
      </div>
      {shouldShowFooter && <Footer />}
      <NotificationContainer />
    </div>
  );
}

AppContent.propTypes = {
  children: PropTypes.node.isRequired
};

function App() {
  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <Router>
        <LanguageProvider>
          <AuthProvider>
            <SidebarProvider>
              <NotificationProvider>
                <LanguageURLHandler />
                <AppContent>
                  <Routes>
                    {/* Admin routes - handle before language routes */}
                    <Route path="/admin" element={<AdminLayout />}>
                      <Route index element={<AdminOverview />} />
                      <Route path="users" element={<AdminUsers />} />
                      <Route path="games" element={<AdminGames />} />
                      <Route path="tickets" element={<AdminTickets />} />
                      <Route path="reports" element={<AdminReports />} />
                      <Route path="reviews" element={<AdminReviews />} />
                      <Route path="logs" element={<AdminLogs />} />
                      <Route path="settings" element={<AdminSettings />} />
                    </Route>

                    {/* Auth callback routes - handle before language routes */}
                    <Route path="/auth/discord/callback" element={<DiscordCallback />} />

                    {/* Language-specific routes */}
                    <Route path="/:lang/*" element={<LanguageRouter />} />

                    {/* Root routes without language prefix - redirect handled by LanguageURLHandler */}
                    <Route path="*" element={<LanguageRouter />} />
                  </Routes>
                </AppContent>
              </NotificationProvider>
            </SidebarProvider>
          </AuthProvider>
        </LanguageProvider>
      </Router>
    </GoogleOAuthProvider>
  );
}

export default App;

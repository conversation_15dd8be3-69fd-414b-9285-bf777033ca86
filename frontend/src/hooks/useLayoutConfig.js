import { useLocation } from 'react-router-dom';

/**
 * Hook to determine layout configuration for different routes
 * This allows you to easily control which pages should have sidebar, header, footer, etc.
 */
export const useLayoutConfig = () => {
  const location = useLocation();

  // Define admin routes that should not have the main website layout (no header, footer, sidebar)
  // Admin routes are accessible without language prefixes
  const adminRoutes = [
    '/admin',
  ];

  // Define specific pages that should not have sidebar but keep header/footer
  const noSidebarOnlyRoutes = [
    '/about',
    // Add more routes here as needed (contact, privacy, terms, etc.)
  ];

  // Combine both for all routes that shouldn't have sidebar
  const allNoSidebarRoutes = [...adminRoutes, ...noSidebarOnlyRoutes];

  // Helper function to check if current route matches any pattern
  const matchesRoute = (routes) => {
    return routes.some(route => {
      // For admin routes, only match exact paths without language prefixes
      if (adminRoutes.includes(route)) {
        return location.pathname === route || location.pathname.startsWith(route + '/');
      }
      // For other routes, match both direct and language-prefixed paths
      return location.pathname.includes(route) ||
             location.pathname.match(new RegExp(`/[a-z]{2}${route}`));
    });
  };

  return {
    shouldShowSidebar: !matchesRoute(allNoSidebarRoutes),
    shouldShowHeader: !matchesRoute(adminRoutes),
    shouldShowFooter: !matchesRoute(adminRoutes),
    isAdminRoute: matchesRoute(adminRoutes),
    currentPath: location.pathname
  };
};

/**
 * Configuration object for easy route management
 * You can import this and modify it to add new routes
 */
export const LAYOUT_CONFIG = {
  // Admin routes (no header, footer, or sidebar)
  ADMIN_ROUTES: [
    '/admin',
  ],
  // Pages that should not have sidebar but keep header/footer
  NO_SIDEBAR_ONLY_ROUTES: [
    '/about',
    // Add more routes here (contact, privacy, terms, etc.)
  ]
};

export default useLayoutConfig;

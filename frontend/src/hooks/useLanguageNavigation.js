import { useNavigate, useLocation } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

/**
 * Custom hook for navigation that automatically includes language prefixes
 */
export const useLanguageNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentLanguage, availableLanguages } = useLanguage();

  // Extract language from URL path
  const getLanguageFromPath = (pathname) => {
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];
    
    // Check if first segment is a valid language code
    if (firstSegment && availableLanguages[firstSegment]) {
      return firstSegment;
    }
    return null;
  };

  // Add language prefix to path
  const addLanguageToPath = (pathname, language = currentLanguage) => {
    const cleanPath = pathname === '/' ? '' : pathname;
    return `/${language}${cleanPath}`;
  };

  // Navigate with language prefix
  const navigateWithLanguage = (to, options = {}) => {
    const targetPath = addLanguageToPath(to, currentLanguage);
    navigate(targetPath, options);
  };

  // Get current path without language prefix
  const getCurrentPathWithoutLanguage = () => {
    const urlLanguage = getLanguageFromPath(location.pathname);
    
    if (urlLanguage) {
      const segments = location.pathname.split('/').filter(Boolean);
      const pathWithoutLang = '/' + segments.slice(1).join('/');
      return pathWithoutLang === '/' ? '/' : pathWithoutLang;
    }
    
    return location.pathname;
  };

  // Check if current path matches a given path (ignoring language prefix)
  const isCurrentPath = (path) => {
    const currentPathWithoutLang = getCurrentPathWithoutLanguage();
    return currentPathWithoutLang === path;
  };

  // Generate language-aware link
  const createLanguageLink = (path, language = currentLanguage) => {
    return addLanguageToPath(path, language);
  };

  // Navigate to home page with current language
  const navigateToHome = () => {
    navigateWithLanguage('/');
  };

  return {
    navigate: navigateWithLanguage,
    navigateToHome,
    location,
    currentLanguage,
    getCurrentPathWithoutLanguage,
    isCurrentPath,
    createLanguageLink,
    getLanguageFromPath,
    addLanguageToPath
  };
};

import { Routes, Route, useLocation } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

// Pages
import HomePage from '../pages/HomePage';
import GamePage from '../pages/GamePage';
import AboutPage from '../pages/AboutPage';
import UploadGamePage from '../pages/UploadGamePage';
import NotFoundPage from '../pages/NotFoundPage';
import DiscordCallback from '../pages/DiscordCallback';
import ProfilePage from '../pages/ProfilePage';
import UserProfilePage from '../pages/UserProfilePage';
import CategoryPage from '../pages/CategoryPage';
import RecentlyPlayedPage from '../pages/RecentlyPlayedPage';
import LanguageRedirect from './LanguageRedirect';

const LanguageRouter = () => {
  const location = useLocation();
  const { availableLanguages } = useLanguage();

  // Extract language from URL path
  const getLanguageFromPath = (pathname) => {
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];

    // Check if first segment is a valid language code
    if (firstSegment && availableLanguages[firstSegment]) {
      return firstSegment;
    }
    return null;
  };

  // Check if this is a language-specific route
  const urlLanguage = getLanguageFromPath(location.pathname);

  // If this is a language-specific route, render the nested routes
  if (urlLanguage) {
    return (
      <Routes>
        <Route index element={<HomePage />} />
        <Route path="about" element={<AboutPage />} />
        <Route path="upload-game" element={<UploadGamePage />} />
        <Route path="auth/discord/callback" element={<DiscordCallback />} />
        <Route path="profile" element={<ProfilePage />} />
        <Route path="user/:userId" element={<UserProfilePage />} />
        <Route path="game/:slug" element={<GamePage />} />
        <Route path="recently-played" element={<RecentlyPlayedPage />} />
        <Route path="new" element={<CategoryPage />} />
        <Route path="trending" element={<CategoryPage />} />
        <Route path="updated" element={<CategoryPage />} />
        <Route path="originals" element={<CategoryPage />} />
        <Route path="multiplayer" element={<CategoryPage />} />
        <Route path="category/:category" element={<CategoryPage />} />
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    );
  }

  // If this is a root route without language prefix, render redirect routes
  return (
    <Routes>
      <Route index element={<LanguageRedirect />} />
      <Route path="about" element={<LanguageRedirect />} />
      <Route path="upload-game" element={<LanguageRedirect />} />
      {/* Discord callback should be handled directly without language redirect */}
      <Route path="auth/discord/callback" element={<DiscordCallback />} />
      <Route path="profile" element={<LanguageRedirect />} />
      <Route path="user/:userId" element={<LanguageRedirect />} />
      <Route path="game/:slug" element={<LanguageRedirect />} />
      <Route path="recently-played" element={<LanguageRedirect />} />
      <Route path="new" element={<LanguageRedirect />} />
      <Route path="trending" element={<LanguageRedirect />} />
      <Route path="updated" element={<LanguageRedirect />} />
      <Route path="originals" element={<LanguageRedirect />} />
      <Route path="multiplayer" element={<LanguageRedirect />} />
      <Route path="category/:category" element={<LanguageRedirect />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default LanguageRouter;

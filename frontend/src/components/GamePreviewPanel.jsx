import { useState } from 'react';
import PropTypes from 'prop-types';
import { FaStar, FaThumbsUp, FaComment, FaTimes, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

/**
 * StarRating Component - Displays star rating
 * @param {number} rating - Rating value from 1-5
 */
const StarRating = ({ rating }) => {
  return (
    <div className="flex gap-1">
      {[...Array(5)].map((_, index) => {
        const starValue = index + 1;
        return (
          <span 
            key={index} 
            className={`${starValue <= rating ? 'text-yellow-400' : 'text-gray-400'}`}
          >
            <FaStar />
          </span>
        );
      })}
    </div>
  );
};

StarRating.propTypes = {
  rating: PropTypes.number.isRequired
};

/**
 * ReviewCard Component - Displays a single review
 * @param {Object} review - Review object with user, rating, content, etc.
 */
const ReviewCard = ({ review }) => {
  return (
    <div className="bg-[#2a2a2a] rounded-lg p-4 mb-4">
      <div className="flex items-start gap-3 mb-3">
        <img src={review.avatar} alt={review.user} className="w-10 h-10 rounded-full object-cover" />
        <div className="flex-1">
          <h4 className="text-white font-semibold">{review.user}</h4>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <span>{review.date}</span>
            <StarRating rating={review.rating} />
          </div>
        </div>
      </div>
      <p className="text-gray-300 mb-3">{review.content}</p>
      <div className="flex gap-4">
        <button className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-200">
          <FaThumbsUp /> {review.likes}
        </button>
        <button className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-200">
          <FaComment /> {review.comments}
        </button>
      </div>
    </div>
  );
};

ReviewCard.propTypes = {
  review: PropTypes.shape({
    avatar: PropTypes.string.isRequired,
    user: PropTypes.string.isRequired,
    date: PropTypes.string.isRequired,
    rating: PropTypes.number.isRequired,
    content: PropTypes.string.isRequired,
    likes: PropTypes.number.isRequired,
    comments: PropTypes.number.isRequired
  }).isRequired
};

/**
 * VideoEmbed Component - Embeds YouTube or Vimeo videos
 * @param {string} url - Video URL
 * @param {boolean} isMain - Whether this is the main video
 */
const VideoEmbed = ({ url, isMain = false }) => {
  // Extract video ID from YouTube or Vimeo URL
  let videoId = '';
  let platform = '';
  
  if (!url) return null;
  
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    platform = 'youtube';
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&]+)/);
    if (match && match[1]) videoId = match[1];
  } else if (url.includes('vimeo.com')) {
    platform = 'vimeo';
    const match = url.match(/vimeo\.com\/(\d+)/);
    if (match && match[1]) videoId = match[1];
  }
  
  if (!videoId) return null;
  
  // Prepare embed URL
  let embedUrl = '';
  if (platform === 'youtube') {
    embedUrl = `https://www.youtube.com/embed/${videoId}`;
  } else if (platform === 'vimeo') {
    embedUrl = `https://player.vimeo.com/video/${videoId}`;
  }
  
  return (
    <div className={`${isMain ? 'aspect-video w-full' : 'aspect-video'} rounded-lg overflow-hidden`}>
      <iframe
        src={embedUrl}
        title={isMain ? "Main Game Video" : "Additional Game Video"}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="w-full h-full"
      ></iframe>
    </div>
  );
};

VideoEmbed.propTypes = {
  url: PropTypes.string,
  isMain: PropTypes.bool
};

VideoEmbed.defaultProps = {
  isMain: false
};

/**
 * CollapsibleSection Component - Expandable/collapsible content section
 * @param {string} title - Section title
 * @param {React.ReactNode} children - Section content
 * @param {boolean} initialOpen - Whether section starts open
 */
const CollapsibleSection = ({ title, children, initialOpen = false }) => {
  const [isOpen, setIsOpen] = useState(initialOpen);
  
  return (
    <div className="mb-6">
      <div 
        className="flex items-center justify-between cursor-pointer p-4 bg-[#2a2a2a] rounded-lg hover:bg-[#333] transition-colors duration-200" 
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-white font-semibold">{title}</h3>
        <span className="text-gray-400">
          {isOpen ? <FaChevronUp /> : <FaChevronDown />}
        </span>
      </div>
      <div className={`overflow-hidden transition-all duration-300 ${isOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}`}>
        <div className="p-4">
          {children}
        </div>
      </div>
    </div>
  );
};

CollapsibleSection.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  initialOpen: PropTypes.bool
};

CollapsibleSection.defaultProps = {
  initialOpen: false
};

/**
 * GamePreviewPanel Component - Preview panel for game page
 * @param {boolean} isOpen - Whether the panel is open
 * @param {Function} onClose - Callback to close the panel
 * @param {Object} gameData - Game data object
 * @param {string} coverImagePreview - Cover image URL
 * @param {Array} screenshotPreviews - Array of screenshot URLs
 */
const GamePreviewPanel = ({ 
  isOpen, 
  onClose, 
  gameData, 
  coverImagePreview,
  screenshotPreviews
}) => {
  // Add the useAuth hook to get the user
  const { user } = useAuth();
  
  // Default placeholder reviews
  const placeholderReviews = [
    {
      user: "EarlyPlayer",
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      content: "Really enjoying this game so far! The mechanics are solid and the atmosphere is great.",
      rating: 4,
      date: new Date().toISOString().split('T')[0],
      likes: 12,
      comments: 3
    }
  ];

  // Format today's date for the release date
  const releaseDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });



  // Create a safe username variable
  const username = user?.username || "You";

  // Check if any platform links exist
  const hasPlatformLinks = gameData.steamUrl || gameData.itchUrl || gameData.epicGamesUrl;

  return (
    <div className={`fixed inset-0 bg-black/80 z-[9999] transition-all duration-300 ${isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'}`}>
      <div className="absolute top-4 right-4 bg-[#1a1a1a] rounded-lg p-4 shadow-2xl">
        <h2 className="text-white text-xl font-bold mb-2">Game Page Preview</h2>
        <button className="absolute top-2 right-2 text-white/70 hover:text-white text-lg p-1 transition-colors duration-200" onClick={onClose}>
          <FaTimes />
        </button>
      </div>
      
      <div className="absolute inset-4 top-20 bg-[#151515] rounded-xl overflow-y-auto">
        <div className="p-6">
          {/* Main Game Details - Updated to match GamePage */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div className="lg:col-span-1">
              {coverImagePreview ? (
                <img 
                  src={coverImagePreview} 
                  alt={gameData.title || "Game Cover"} 
                  className="w-full aspect-[3/4] object-cover rounded-lg" 
                />
              ) : (
                <div className="w-full aspect-[3/4] bg-[#2a2a2a] rounded-lg flex items-center justify-center text-gray-400">
                  No cover image
                </div>
              )}
            </div>
            <div className="lg:col-span-2">
              <h1 className="text-4xl font-bold text-white mb-4">
                {gameData.title || "Your Game Title"}
              </h1>
              <div className="space-y-2 mb-6">
                {/* Simplified metadata that matches GamePage */}
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 font-medium">Published by:</span> 
                  <span className="text-white">{username}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 font-medium">Release Date:</span> 
                  <span className="text-white">{releaseDate}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 font-medium">Genre:</span> 
                  <span className="text-white">{gameData.genre || "Not specified"}</span>
                </div>
                
                {/* Platform links */}
                {hasPlatformLinks && (
                  <div className="flex items-center gap-2">
                    <span className="text-gray-400 font-medium">Available on:</span>
                    <div className="flex gap-2">
                      {gameData.steamUrl && (
                        <a href={gameData.steamUrl} target="_blank" rel="noopener noreferrer" className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors duration-200">
                          Steam
                        </a>
                      )}
                      {gameData.itchUrl && (
                        <a href={gameData.itchUrl} target="_blank" rel="noopener noreferrer" className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors duration-200">
                          itch.io
                        </a>
                      )}
                      {gameData.epicGamesUrl && (
                        <a href={gameData.epicGamesUrl} target="_blank" rel="noopener noreferrer" className="px-3 py-1 bg-gray-800 text-white rounded text-sm hover:bg-gray-900 transition-colors duration-200">
                          Epic Games
                        </a>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="mb-6">
                <h3 className="text-xl font-bold text-white mb-3">About</h3>
                <p className="text-gray-300 leading-relaxed">{gameData.description || "Your game description will appear here. Make sure to provide a compelling description of your game to attract players."}</p>
              </div>
              
              {/* Integrated main video directly under description with no heading */}
              {gameData.mainVideo && (
                <div className="mb-6">
                  <VideoEmbed url={gameData.mainVideo} isMain={true} />
                </div>
              )}
            </div>
          </div>

          {/* Screenshots in a collapsible section - Matches GamePage */}
          {screenshotPreviews.length > 0 && (
            <CollapsibleSection title="Screenshots" initialOpen={true}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {screenshotPreviews.map((screenshot, index) => (
                  <div key={index} className="aspect-video rounded-lg overflow-hidden">
                    <img 
                      src={screenshot} 
                      alt={`Screenshot ${index + 1}`} 
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                ))}
              </div>
            </CollapsibleSection>
          )}

          {/* Additional Videos in a collapsible section - Matches GamePage */}
          {gameData.additionalVideos && gameData.additionalVideos.some(url => url) && (
            <CollapsibleSection title="More Videos">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {gameData.additionalVideos.map((url, index) => (
                  url && <VideoEmbed key={index} url={url} />
                ))}
              </div>
            </CollapsibleSection>
          )}

          {/* Reviews Section */}
          <div className="mt-8">
            <h2 className="text-2xl font-bold text-white mb-6">Reviews</h2>
            
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-white mb-4">Write a Review</h3>
              <div className="bg-[#2a2a2a] rounded-lg p-6 text-center">
                <p className="text-gray-400">Players will be able to leave reviews for your game here.</p>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Sample Reviews</h3>
              <div>
                {placeholderReviews.map((review, index) => (
                  <ReviewCard key={index} review={review} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

GamePreviewPanel.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  gameData: PropTypes.shape({
    title: PropTypes.string,
    genre: PropTypes.string,
    description: PropTypes.string,
    priceModel: PropTypes.string,
    price: PropTypes.number,
    creditPrice: PropTypes.number,
    mainVideo: PropTypes.string,
    additionalVideos: PropTypes.arrayOf(PropTypes.string),
    steamUrl: PropTypes.string,
    itchUrl: PropTypes.string,
    epicGamesUrl: PropTypes.string
  }).isRequired,
  coverImagePreview: PropTypes.string,
  screenshotPreviews: PropTypes.arrayOf(PropTypes.string)
};

GamePreviewPanel.defaultProps = {
  coverImagePreview: null,
  screenshotPreviews: []
};

export default GamePreviewPanel;

import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import { getUserCountryCode, getLanguageFromCountry } from '../services/geolocationService';
import GameLoader from './GameLoader';

/**
 * Component that handles initial language detection and redirect
 * This should be used for the root route without language prefix
 */
const LanguageRedirect = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { availableLanguages } = useLanguage();
  const [isDetecting, setIsDetecting] = useState(true);

  // Storage key for language preference
  const LANGUAGE_STORAGE_KEY = 'indierepo_language';
  const DEFAULT_LANGUAGE = 'en';

  useEffect(() => {
    const detectAndRedirect = async () => {
      try {
        // Check if user has a saved language preference
        const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);
        let selectedLanguage = DEFAULT_LANGUAGE;

        if (savedLanguage && availableLanguages[savedLanguage]) {
          // Use saved language preference
          selectedLanguage = savedLanguage;
        } else {
          // Detect language from user's country
          try {
            const userCountry = await getUserCountryCode();
            selectedLanguage = getLanguageFromCountry(userCountry);
          } catch (error) {
            console.warn('Failed to detect language from geolocation, using default:', error);
            selectedLanguage = DEFAULT_LANGUAGE;
          }
        }

        // Construct the redirect URL with language prefix
        const targetPath = `/${selectedLanguage}${location.pathname}${location.search}${location.hash}`;
        
        
        // Redirect to language-specific URL
        navigate(targetPath, { replace: true });

      } catch (error) {
        console.error('Error during language detection:', error);
        // Fallback to default language
        const targetPath = `/${DEFAULT_LANGUAGE}${location.pathname}${location.search}${location.hash}`;
        navigate(targetPath, { replace: true });
      } finally {
        setIsDetecting(false);
      }
    };

    detectAndRedirect();
  }, [navigate, location, availableLanguages]);

  // Show loading while detecting language
  if (isDetecting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <GameLoader size="xl" variant="pixel" />
          <p className="text-gray-300 mt-4">Detecting your language...</p>
        </div>
      </div>
    );
  }

  // This should not render anything after redirect
  return null;
};

export default LanguageRedirect;

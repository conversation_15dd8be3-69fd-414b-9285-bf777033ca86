import { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import GameCard from './GameCard';

/**
 * CategorySection Component - Horizontal scrolling section for a game category
 * @param {string} title - Section title (e.g., "Driving Games")
 * @param {Array} games - Array of games for this category
 * @param {string} categorySlug - URL slug for the category (e.g., "driving")
 * @param {boolean} loading - Loading state
 * @param {string} className - Additional CSS classes
 */
const CategorySection = ({ 
  title, 
  games = [], 
  categorySlug, 
  loading = false, 
  className = '' 
}) => {
  const scrollContainerRef = useRef(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);

  // Check scroll position and update arrow states
  const checkScrollPosition = () => {
    if (!scrollContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
  };

  // Initialize scroll position check
  useEffect(() => {
    checkScrollPosition();
    
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollPosition);
      return () => container.removeEventListener('scroll', checkScrollPosition);
    }
  }, [games]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      checkScrollPosition();
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Scroll functions
  const scrollLeft = () => {
    if (!scrollContainerRef.current || isScrolling) return;

    setIsScrolling(true);
    // Calculate scroll amount based on viewport width
    const viewportWidth = window.innerWidth;
    let scrollAmount;
    if (viewportWidth < 640) { // sm breakpoint
      scrollAmount = 260; // ~2 mobile cards (110px + 16px gap) * 2
    } else if (viewportWidth < 768) { // md breakpoint
      scrollAmount = 312; // ~2 small cards (140px + 16px gap) * 2
    } else if (viewportWidth < 1024) { // lg breakpoint
      scrollAmount = 392; // ~2 medium cards (180px + 16px gap) * 2
    } else {
      scrollAmount = 480; // ~2 large cards (230px + 16px gap) * 2
    }

    scrollContainerRef.current.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });

    setTimeout(() => setIsScrolling(false), 300);
  };

  const scrollRight = () => {
    if (!scrollContainerRef.current || isScrolling) return;

    setIsScrolling(true);
    // Calculate scroll amount based on viewport width
    const viewportWidth = window.innerWidth;
    let scrollAmount;
    if (viewportWidth < 640) { // sm breakpoint
      scrollAmount = 260; // ~2 mobile cards (110px + 16px gap) * 2
    } else if (viewportWidth < 768) { // md breakpoint
      scrollAmount = 312; // ~2 small cards (140px + 16px gap) * 2
    } else if (viewportWidth < 1024) { // lg breakpoint
      scrollAmount = 392; // ~2 medium cards (180px + 16px gap) * 2
    } else {
      scrollAmount = 480; // ~2 large cards (230px + 16px gap) * 2
    }

    scrollContainerRef.current.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });

    setTimeout(() => setIsScrolling(false), 300);
  };

  if (loading) {
    return (
      <section className={`mb-8 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="h-6 bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-4 bg-gray-700 rounded w-20 animate-pulse"></div>
        </div>
        
        <div className="flex gap-4 overflow-hidden">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="flex-shrink-0 w-[110px] sm:w-[140px] md:w-[180px] lg:w-[230px] h-[130px] bg-gray-700 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </section>
    );
  }

  if (!games || games.length === 0) {
    return null;
  }

  return (
    <section className={`mb-8 relative ${className}`}>
      {/* Section Header */}
      <div className="flex items-center justify-between mb-4 px-4">
        <h2 className="text-xl font-bold text-white">{title}</h2>
        <Link 
          to={`/category/${categorySlug}`}
          className="text-sm text-gray-400 hover:text-white transition-colors duration-200"
        >
          View more
        </Link>
      </div>

      {/* Scrollable Games Container */}
      <div className="relative">
        {/* Left Arrow */}
        {canScrollLeft && (
          <button
            onClick={scrollLeft}
            disabled={isScrolling}
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-black/70 hover:bg-black/90 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
            aria-label="Scroll left"
          >
            <FaChevronLeft className="text-sm" />
          </button>
        )}

        {/* Right Arrow */}
        {canScrollRight && (
          <button
            onClick={scrollRight}
            disabled={isScrolling}
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-black/70 hover:bg-black/90 rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
            aria-label="Scroll right"
          >
            <FaChevronRight className="text-sm" />
          </button>
        )}

        {/* Games Scroll Container */}
        <div
          ref={scrollContainerRef}
          className="flex gap-4 overflow-x-auto scrollbar-hide scroll-smooth px-4"
          style={{ 
            scrollbarWidth: 'none',
            msOverflowStyle: 'none'
          }}
        >
          {games.map((game, index) => (
            <div
              key={game.id || index}
              className="flex-shrink-0 w-[110px] sm:w-[140px] md:w-[180px] lg:w-[230px] h-[130px]"
            >
              <GameCard
                game={game}
                size="compact"
                className="h-full rounded-lg overflow-hidden"
              />
            </div>
          ))}
        </div>
      </div>


    </section>
  );
};

CategorySection.propTypes = {
  title: PropTypes.string.isRequired,
  games: PropTypes.array,
  categorySlug: PropTypes.string.isRequired,
  loading: PropTypes.bool,
  className: PropTypes.string
};

export default CategorySection;

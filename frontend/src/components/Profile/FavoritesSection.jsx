import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { FaHeart } from 'react-icons/fa';
import GameCard from '../GameCard';
import { toggleFavoriteGame } from '../../services/gameService';
import { useLanguageNavigation } from '../../hooks/useLanguageNavigation';
import { getGameSlug } from '../../utils/slugUtils';

const FavoritesSection = ({ favorites, onFavoritesUpdate }) => {
  const { createLanguageLink } = useLanguageNavigation();

  const handleRemoveFavorite = async (gameId) => {
    try {
      await toggleFavoriteGame(gameId);
      // Call the parent component to refresh favorites
      if (onFavoritesUpdate) {
        onFavoritesUpdate();
      }
    } catch (error) {
      console.error('Error removing from favorites:', error);
    }
  };

  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Favorites</h1>
      
      {favorites.length > 0 ? (
        <div className="space-y-6">
          {/* Use horizontal scrolling layout like home page */}
          <div className="relative">
            <div
              className="flex gap-4 overflow-x-auto scroll-smooth px-4"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none'
              }}
            >
              {favorites.map(game => (
                <div
                  key={game.id}
                  className="flex-shrink-0 w-[110px] sm:w-[140px] md:w-[180px] lg:w-[230px]"
                >
                  <div className="space-y-3">
                    <div className="h-[130px]">
                      <GameCard
                        game={game}
                        size="compact"
                        className="h-full rounded-lg overflow-hidden"
                      />
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleRemoveFavorite(game.id)}
                        className="flex-1 bg-gray-600 hover:bg-gray-500 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                      >
                        Remove
                      </button>
                      <Link
                        to={createLanguageLink(`/game/${getGameSlug(game)}`)}
                        className="flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 text-center no-underline"
                      >
                        Play
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Hide scrollbar for webkit browsers */}
            <style>
              {`.overflow-x-auto::-webkit-scrollbar { display: none; }`}
            </style>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaHeart className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Your favorites is empty</h3>
          <p className="text-gray-400 mb-6">Tap the heart on games you love to add them to your favorites</p>
          <Link
            to={createLanguageLink("/")}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Discover Games
          </Link>
        </div>
      )}
    </section>
  );
};

FavoritesSection.propTypes = {
  favorites: PropTypes.array.isRequired,
  onFavoritesUpdate: PropTypes.func
};

export default FavoritesSection;

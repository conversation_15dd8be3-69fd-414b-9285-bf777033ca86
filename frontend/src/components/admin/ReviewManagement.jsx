import { useState, useEffect } from 'react';
import axios from 'axios';
import { FaEdit, FaTrash, FaStar, FaUser, FaGamepad, FaSearch } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { useNotification } from '../../context/NotificationContext';
import { API_URL } from '../../config/env';

const ReviewManagement = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useNotification();
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [editContent, setEditContent] = useState('');

  useEffect(() => {
    fetchReviews();
  }, [currentPage, searchTerm]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        limit: 20
      });

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      const response = await axios.get(`${API_URL}/moderation/reviews?${params}`, {
        withCredentials: true
      });

      setReviews(response.data.reviews || []);
      setTotalPages(response.data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      showError('Failed to load reviews. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEditReview = async (review) => {
    try {
      // Fetch the full review content
      const response = await axios.get(`${API_URL}/moderation/reviews/${review.id}`, {
        withCredentials: true
      });
      setEditContent(response.data.comment || '');
      setSelectedReview(review);
      setShowEditModal(true);
    } catch (error) {
      console.error('Error fetching review:', error);
      showError('Failed to load review content.');
    }
  };

  const handleSaveEdit = async () => {
    if (!selectedReview) return;

    try {
      await axios.put(`${API_URL}/moderation/reviews/${selectedReview.id}`, {
        newContent: {
          comment: editContent.trim()
        },
        reason: 'Content moderation - edited via admin panel'
      }, {
        withCredentials: true
      });

      setShowEditModal(false);
      setSelectedReview(null);
      setEditContent('');
      showSuccess('Review updated successfully.');
      fetchReviews();
    } catch (error) {
      console.error('Error updating review:', error);
      showError('Failed to update review. Please try again.');
    }
  };

  const handleDeleteReview = async (reviewId) => {
    if (!window.confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/moderation/reviews/${reviewId}`, {
        data: {
          reason: 'Content removed via admin panel'
        },
        withCredentials: true
      });

      showSuccess('Review deleted successfully.');
      fetchReviews();
    } catch (error) {
      console.error('Error deleting review:', error);
      showError('Failed to delete review. Please try again.');
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <FaStar
        key={index}
        className={`text-sm ${index < rating ? 'text-yellow-400' : 'text-gray-600'}`}
      />
    ));
  };

  if (loading && reviews.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-white text-lg">Loading reviews...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end items-center mb-6">
        <div className="flex items-center gap-4">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search reviews..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Reviews Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Review Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Game
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Rating
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {reviews.map((review) => (
                <tr key={review.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div className="max-w-xs">
                      <div className="text-sm font-medium text-white mb-1">
                        Review #{review.id}
                      </div>
                      <div className="text-sm text-gray-300 truncate">
                        {review.comment || 'No content'}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <span className="text-sm text-white">{review.user_username || 'Unknown'}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaGamepad className="text-gray-400 mr-2" />
                      <span className="text-sm text-white">{review.game_title || 'Unknown'}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-1">
                      {renderStars(review.rating || 0)}
                      <span className="text-sm text-gray-300 ml-2">({review.rating || 0}/5)</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {new Date(review.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      {/* Edit and Delete actions for admin and moderator */}
                      {(user?.role === 'admin' || user?.role === 'moderator') && (
                        <>
                          <button
                            onClick={() => handleEditReview(review)}
                            className="text-blue-400 hover:text-blue-300 flex items-center gap-1"
                            title="Edit Review"
                          >
                            <FaEdit />
                          </button>

                          <button
                            onClick={() => handleDeleteReview(review.id)}
                            className="text-red-400 hover:text-red-300 flex items-center gap-1"
                            title="Delete Review"
                          >
                            <FaTrash />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-gray-700 px-6 py-3 flex items-center justify-between border-t border-gray-600">
            <div className="text-sm text-gray-300">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Edit Review Modal */}
      {showEditModal && selectedReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-4">
              Edit Review #{selectedReview.id}
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Review Content:
              </label>
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                rows={6}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
                placeholder="Enter review content..."
              />
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setSelectedReview(null);
                  setEditContent('');
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!loading && reviews.length === 0 && (
        <div className="text-center py-12 bg-gray-800 rounded-lg border border-gray-700 mt-6">
          <p className="text-gray-400 text-lg">No reviews found.</p>
        </div>
      )}
    </div>
  );
};

export default ReviewManagement;

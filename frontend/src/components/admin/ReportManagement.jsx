import { useState, useEffect } from 'react';
import { <PERSON>a<PERSON>lag, <PERSON>a<PERSON>ser, FaGamepad, FaComment, FaCheck, FaTimes, FaEdit, FaTrash, FaEye } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../../config/env';
import GameLoader from '../GameLoader';
import { useAuth } from '../../context/AuthContext';
import { useNotification } from '../../context/NotificationContext';

const ReportManagement = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useNotification();
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [editDescription, setEditDescription] = useState('');
  const [editStatus, setEditStatus] = useState('');
  const [showEditContentModal, setShowEditContentModal] = useState(false);
  const [editReviewContent, setEditReviewContent] = useState('');

  useEffect(() => {
    fetchReports();
  }, [currentPage, filterStatus, filterType]);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        limit: 20
      });

      // Only add status filter if not 'all'
      if (filterStatus && filterStatus !== 'all') {
        params.append('status', filterStatus);
      }

      // Only add reportType filter if not 'all'
      if (filterType && filterType !== 'all') {
        params.append('reportType', filterType);
      }

      const response = await axios.get(`${API_URL}/reports?${params}`, {
        withCredentials: true
      });

      setReports(response.data.reports || []);
      setTotalPages(response.data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching reports:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      setReports([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateReportStatus = async (reportId, status) => {
    try {
      await axios.put(`${API_URL}/reports/${reportId}/status`, {
        status
      }, {
        withCredentials: true
      });
      fetchReports();
    } catch (error) {
      console.error('Error updating report status:', error);
    }
  };

  const handleEditReport = (report) => {
    setSelectedReport(report);
    setEditDescription(report.description || '');
    setEditStatus(report.status);
    setShowEditModal(true);
  };

  const handleSaveEdit = async () => {
    if (!selectedReport) return;

    try {
      await axios.put(`${API_URL}/reports/${selectedReport.id}`, {
        description: editDescription.trim(),
        status: editStatus
      }, {
        withCredentials: true
      });

      setShowEditModal(false);
      setSelectedReport(null);
      fetchReports();
    } catch (error) {
      console.error('Error updating report:', error);
      showError('Failed to update report. Please try again.');
    }
  };

  const handleDeleteReport = async (reportId) => {
    if (!window.confirm('Are you sure you want to delete this report? This action cannot be undone.')) {
      return;
    }

    try {
      await axios.delete(`${API_URL}/reports/${reportId}`, {
        withCredentials: true
      });
      fetchReports();
    } catch (error) {
      console.error('Error deleting report:', error);
      showError('Failed to delete report. Please try again.');
    }
  };

  const handleEditReportedContent = async (report) => {
    // Determine the type of reported content and redirect to appropriate edit page
    if (report.reported_review_id) {
      try {
        // Fetch the review content first
        const response = await axios.get(`${API_URL}/moderation/reviews/${report.reported_review_id}`, {
          withCredentials: true
        });
        setEditReviewContent(response.data.comment || '');
        setSelectedReport(report);
        setShowEditContentModal(true);
      } catch (error) {
        console.error('Error fetching review:', error);
        showError('Failed to load review content.');
      }
    } else if (report.reported_game_id) {
      // For games, redirect to game edit page or show modal
      showError('Game editing functionality will be implemented');
    } else if (report.reported_user_id) {
      // For users, show user management actions
      showError('User management functionality is available in User Management section');
    }
  };

  const handleSaveReviewEdit = async () => {
    if (!selectedReport || !selectedReport.reported_review_id) return;

    try {
      await axios.put(`${API_URL}/moderation/reviews/${selectedReport.reported_review_id}`, {
        newContent: {
          comment: editReviewContent.trim()
        },
        reason: 'Content moderation - edited via admin panel'
      }, {
        withCredentials: true
      });

      setShowEditContentModal(false);
      setSelectedReport(null);
      setEditReviewContent('');
      showSuccess('Review updated successfully.');
      fetchReports();
    } catch (error) {
      console.error('Error updating review:', error);
      showError('Failed to update review. Please try again.');
    }
  };

  const handleDeleteReportedContent = async (report) => {
    const contentType = report.reported_game_id ? 'game' :
                       report.reported_review_id ? 'review' :
                       report.reported_user_id ? 'user' : 'content';

    if (!window.confirm(`Are you sure you want to delete this ${contentType}? This action cannot be undone.`)) {
      return;
    }

    try {
      let endpoint = '';
      if (report.reported_review_id) {
        endpoint = `/moderation/reviews/${report.reported_review_id}`;
      } else if (report.reported_game_id) {
        endpoint = `/moderation/games/${report.reported_game_id}`;
      } else if (report.reported_user_id) {
        // For users, we should ban them instead of delete
        if (window.confirm('Users cannot be deleted. Would you like to ban this user instead?')) {
          endpoint = `/moderation/users/${report.reported_user_id}/ban`;
          await axios.post(`${API_URL}${endpoint}`, {
            reason: 'Banned due to reported content',
            duration: null // permanent ban
          }, {
            withCredentials: true
          });
          showSuccess('User has been banned successfully.');
          fetchReports();
          return;
        } else {
          return;
        }
      }

      if (endpoint) {
        if (report.reported_review_id) {
          // For reviews, we need to send a reason in the request body
          await axios.delete(`${API_URL}${endpoint}`, {
            data: {
              reason: 'Content removed due to reported violations'
            },
            withCredentials: true
          });
        } else {
          // For games, use regular delete
          await axios.delete(`${API_URL}${endpoint}`, {
            withCredentials: true
          });
        }
        showSuccess(`${contentType.charAt(0).toUpperCase() + contentType.slice(1)} deleted successfully.`);
        fetchReports();
      }
    } catch (error) {
      console.error('Error deleting reported content:', error);
      showError('Failed to delete content. Please try again.');
    }
  };

  const getReportTypeIcon = (report) => {
    // Determine what type of content is being reported based on which ID is present
    if (report.reported_game_id) return <FaGamepad className="text-blue-400" />;
    if (report.reported_review_id) return <FaComment className="text-green-400" />;
    if (report.reported_user_id) return <FaUser className="text-purple-400" />;
    return <FaFlag className="text-gray-400" />;
  };

  const getReportedContentType = (report) => {
    if (report.reported_game_id) return 'Game';
    if (report.reported_review_id) return 'Review';
    if (report.reported_user_id) return 'User';
    return 'Unknown';
  };

  const getReasonColor = (reportType) => {
    switch (reportType) {
      case 'offensive_content': return 'bg-red-900 text-red-200 border-red-600';
      case 'sexually_explicit': return 'bg-pink-900 text-pink-200 border-pink-600';
      case 'harassment': return 'bg-purple-900 text-purple-200 border-purple-600';
      case 'spam': return 'bg-yellow-900 text-yellow-200 border-yellow-600';
      case 'copyright_violation': return 'bg-orange-900 text-orange-200 border-orange-600';
      case 'inappropriate_content': return 'bg-red-800 text-red-200 border-red-500';
      case 'rule_violation': return 'bg-orange-900 text-orange-200 border-orange-600';
      case 'fake_content': return 'bg-indigo-900 text-indigo-200 border-indigo-600';
      case 'other': return 'bg-gray-700 text-gray-300 border-gray-600';
      default: return 'bg-gray-700 text-gray-300 border-gray-600';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-900 text-yellow-200 border-yellow-600';
      case 'reviewed': return 'bg-blue-900 text-blue-200 border-blue-600';
      case 'resolved': return 'bg-green-900 text-green-200 border-green-600';
      case 'dismissed': return 'bg-gray-700 text-gray-300 border-gray-600';
      default: return 'bg-gray-700 text-gray-300 border-gray-600';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <GameLoader size="lg" variant="gamepad" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex justify-end items-center mb-6">
        <div className="flex items-center gap-4">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="reviewed">Reviewed</option>
            <option value="resolved">Resolved</option>
            <option value="dismissed">Dismissed</option>
          </select>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
            <option value="all">All Types</option>
            <option value="offensive_content">Offensive Content</option>
            <option value="sexually_explicit">Sexually Explicit</option>
            <option value="harassment">Harassment</option>
            <option value="spam">Spam</option>
            <option value="copyright_violation">Copyright Violation</option>
            <option value="inappropriate_content">Inappropriate Content</option>
            <option value="rule_violation">Rule Violation</option>
            <option value="fake_content">Fake Content</option>
            <option value="other">Other</option>
          </select>

          <button
            onClick={fetchReports}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Reports Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Report
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Reporter
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {reports.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <FaFlag className="text-gray-500 text-4xl mb-4" />
                      <h3 className="text-lg font-medium text-gray-400 mb-2">No Reports Found</h3>
                      <p className="text-gray-500">There are no reports matching your current filters.</p>
                    </div>
                  </td>
                </tr>
              ) : (
                reports.map((report) => (
                <tr key={report.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-white">
                        Report #{report.id}
                      </div>
                      <div className="text-sm text-gray-400 truncate max-w-xs">
                        {report.description || 'No description provided'}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <span className="text-sm text-white">
                        {report.reporter_username || 'Anonymous'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getReportTypeIcon(report)}
                      <span className="text-sm text-white">
                        {getReportedContentType(report)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getReasonColor(report.report_type)}`}>
                      {(report.report_type || 'unknown').replace(/_/g, ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(report.status)}`}>
                      {report.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {new Date(report.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2 flex-wrap">
                      {/* Admin and Moderator actions */}
                      {(user?.role === 'admin' || user?.role === 'moderator') && (
                        <div className="flex flex-wrap gap-2">
                          {/* Report Actions */}
                          <div className="flex gap-1 border-r border-gray-600 pr-2">
                            <button
                              onClick={() => handleEditReport(report)}
                              className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                              title="Edit Report Details"
                            >
                              <FaEdit className="text-xs" />
                              Edit Report
                            </button>

                            <button
                              onClick={() => handleDeleteReport(report.id)}
                              className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                              title="Delete This Report"
                            >
                              <FaTrash className="text-xs" />
                              Delete Report
                            </button>
                          </div>

                          {/* Content Actions */}
                          <div className="flex gap-1">
                            <button
                              onClick={() => handleEditReportedContent(report)}
                              className="px-2 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                              title="Edit the Reported Content"
                            >
                              <FaEdit className="text-xs" />
                              Edit Content
                            </button>

                            <button
                              onClick={() => handleDeleteReportedContent(report)}
                              className="px-2 py-1 bg-orange-600 hover:bg-orange-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                              title="Delete the Reported Content"
                            >
                              <FaTrash className="text-xs" />
                              Delete Content
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Status Actions */}
                      <div className="flex gap-1 mt-2">
                        {report.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleUpdateReportStatus(report.id, 'resolved')}
                              className="px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                              title="Mark Report as Resolved"
                            >
                              <FaCheck className="text-xs" />
                              Resolve
                            </button>
                            <button
                              onClick={() => handleUpdateReportStatus(report.id, 'dismissed')}
                              className="px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                              title="Dismiss Report"
                            >
                              <FaTimes className="text-xs" />
                              Dismiss
                            </button>
                          </>
                        )}

                        {report.status === 'reviewed' && (
                          <button
                            onClick={() => handleUpdateReportStatus(report.id, 'resolved')}
                            className="px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded flex items-center gap-1 transition-colors"
                            title="Mark Report as Resolved"
                          >
                            <FaCheck className="text-xs" />
                            Resolve
                          </button>
                        )}
                      </div>
                    </div>
                  </td>
                </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-400">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>

      {/* Edit Report Modal */}
      {showEditModal && selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-4">Edit Report #{selectedReport.id}</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={editDescription}
                  onChange={(e) => setEditDescription(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="4"
                  placeholder="Report description..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={editStatus}
                  onChange={(e) => setEditStatus(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="under_review">Under Review</option>
                  <option value="reviewed">Reviewed</option>
                  <option value="resolved">Resolved</option>
                  <option value="dismissed">Dismissed</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Review Content Modal */}
      {showEditContentModal && selectedReport && selectedReport.reported_review_id && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-4">
              Edit Review (ID: {selectedReport.reported_review_id})
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Review Content
                </label>
                <textarea
                  value={editReviewContent}
                  onChange={(e) => setEditReviewContent(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="6"
                  placeholder="Review content..."
                />
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={() => {
                  setShowEditContentModal(false);
                  setSelectedReport(null);
                  setEditReviewContent('');
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveReviewEdit}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Save Review
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportManagement;

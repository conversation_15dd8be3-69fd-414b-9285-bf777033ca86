import { useState, useEffect } from 'react';
import { FaShieldAlt, FaUser, FaGamepad, FaComment, FaClock } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../../config/env';
import GameLoader from '../GameLoader';

const ModerationLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterAction, setFilterAction] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchLogs();
  }, [currentPage, filterAction]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        limit: 20
      });

      // Only add actionType filter if not 'all'
      if (filterAction && filterAction !== 'all') {
        params.append('actionType', filterAction);
      }

      const response = await axios.get(`${API_URL}/moderation/logs?${params}`, {
        withCredentials: true
      });

      setLogs(response.data.logs);
      setTotalPages(response.data.totalPages);
    } catch (error) {
      console.error('Error fetching moderation logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (action) => {
    switch (action) {
      case 'ban_user':
      case 'unban_user':
        return <FaUser className="text-red-400" />;
      case 'delete_game':
      case 'restore_game':
        return <FaGamepad className="text-blue-400" />;
      case 'delete_review':
      case 'edit_review':
        return <FaComment className="text-green-400" />;
      default:
        return <FaShieldAlt className="text-gray-400" />;
    }
  };

  const getActionColor = (action) => {
    if (!action || typeof action !== 'string') {
      return 'bg-gray-900 text-gray-200 border-gray-600';
    }

    if (action.includes('ban') || action.includes('delete')) {
      return 'bg-red-900 text-red-200 border-red-600';
    } else if (action.includes('unban') || action.includes('restore')) {
      return 'bg-green-900 text-green-200 border-green-600';
    } else if (action.includes('edit')) {
      return 'bg-yellow-900 text-yellow-200 border-yellow-600';
    }
    return 'bg-blue-900 text-blue-200 border-blue-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <GameLoader size="lg" variant="gamepad" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex justify-end items-center mb-6">
        <div className="flex items-center gap-4">
          <select
            value={filterAction}
            onChange={(e) => setFilterAction(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          >
            <option value="all">All Actions</option>
            <option value="ban_user">Ban User</option>
            <option value="unban_user">Unban User</option>
            <option value="delete_game">Delete Game</option>
            <option value="restore_game">Restore Game</option>
            <option value="delete_review">Delete Review</option>
            <option value="edit_review">Edit Review</option>
          </select>

          <button
            onClick={fetchLogs}
            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors duration-200 text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Logs Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Moderator
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Target
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {logs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getActionIcon(log.action)}
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getActionColor(log.action)}`}>
                        {log.action ? log.action.replace('_', ' ') : 'Unknown Action'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FaUser className="text-gray-400 mr-2" />
                      <span className="text-sm text-white">
                        {log.moderator?.username || 'System'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-white">
                      {log.target_type || 'Unknown'}: {log.target_id || 'N/A'}
                    </div>
                    <div className="text-sm text-gray-400">
                      {log.target_details || 'No details available'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-300 max-w-xs truncate">
                      {log.reason || 'No reason provided'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-400">
                      <FaClock className="mr-1" />
                      {log.created_at ? new Date(log.created_at).toLocaleString() : 'Unknown date'}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-400">
          Page {currentPage} of {totalPages}
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 bg-gray-700 text-white rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>

      {logs.length === 0 && !loading && (
        <div className="text-center py-12">
          <FaShieldAlt className="text-gray-500 text-4xl mx-auto mb-4" />
          <p className="text-gray-400">No moderation logs found</p>
        </div>
      )}
    </div>
  );
};

export default ModerationLogs;

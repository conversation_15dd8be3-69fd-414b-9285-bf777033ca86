import { useEffect, useState } from 'react';
import { FaCheckCircle, FaExclamationCircle, FaExclamationTriangle, FaInfoCircle, FaTimes } from 'react-icons/fa';
import { useNotification, NOTIFICATION_TYPES } from '../context/NotificationContext';

/**
 * Individual notification component
 */
const NotificationItem = ({ notification, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleRemove = () => {
    setIsRemoving(true);
    setTimeout(() => {
      onRemove(notification.id);
    }, 300); // Match animation duration
  };

  const getIcon = () => {
    switch (notification.type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return <FaCheckCircle className="text-green-400" />;
      case NOTIFICATION_TYPES.ERROR:
        return <FaExclamationCircle className="text-red-400" />;
      case NOTIFICATION_TYPES.WARNING:
        return <FaExclamationTriangle className="text-yellow-400" />;
      case NOTIFICATION_TYPES.INFO:
      default:
        return <FaInfoCircle className="text-blue-400" />;
    }
  };

  const getBackgroundColor = () => {
    switch (notification.type) {
      case NOTIFICATION_TYPES.SUCCESS:
        return 'bg-green-900/90 border-green-500/50';
      case NOTIFICATION_TYPES.ERROR:
        return 'bg-red-900/90 border-red-500/50';
      case NOTIFICATION_TYPES.WARNING:
        return 'bg-yellow-900/90 border-yellow-500/50';
      case NOTIFICATION_TYPES.INFO:
      default:
        return 'bg-blue-900/90 border-blue-500/50';
    }
  };

  return (
    <div
      className={`
        relative flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm shadow-lg
        transform transition-all duration-300 ease-in-out
        ${getBackgroundColor()}
        ${isVisible && !isRemoving 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
        }
      `}
    >
      {/* Icon */}
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>

      {/* Message */}
      <div className="flex-1 text-white text-sm leading-relaxed">
        {notification.message}
      </div>

      {/* Close button */}
      <button
        onClick={handleRemove}
        className="flex-shrink-0 text-gray-400 hover:text-white transition-colors duration-200 p-1"
        aria-label="Close notification"
      >
        <FaTimes className="text-xs" />
      </button>
    </div>
  );
};

/**
 * Notification container component
 */
const NotificationContainer = () => {
  const { notifications, removeNotification } = useNotification();

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-[10001] space-y-3 max-w-sm w-full">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onRemove={removeNotification}
        />
      ))}
    </div>
  );
};

export default NotificationContainer;

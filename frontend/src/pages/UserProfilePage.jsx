import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { FaStar, FaGamepad, FaComment, FaThumbsUp, FaThumbsDown } from 'react-icons/fa';
import PropTypes from 'prop-types';
// Import both real and mock functions
import {
  getUserProfile, getUserReviews, getUserPosts,
  getMockUserProfile, getMockUserReviews, getMockUserPosts
} from '../services/userService';
import { avatarPlaceholder, gamePlaceholder } from '../assets/placeholders';
import ReportButton from '../components/ReportButton';
import { useAuth } from '../context/AuthContext';

// For development, use mock data until backend is ready
const useMockData = true; // Set to false when backend is ready

// Override functions if using mock data
const getProfileFn = useMockData ? getMockUserProfile : getUserProfile;
const getReviewsFn = useMockData ? getMockUserReviews : getUserReviews;
const getPostsFn = useMockData ? getMockUserPosts : getUserPosts;

// Star rating component (reused from GamePage)
const StarRating = ({ rating }) => {
  return (
    <div className="flex items-center gap-1">
      {[...Array(5)].map((_, index) => {
        const starValue = index + 1;
        return (
          <span 
            key={index} 
            className={`text-lg transition-colors duration-200 ${
              starValue <= rating ? 'text-yellow-400' : 'text-gray-600'
            }`}
          >
            <FaStar />
          </span>
        );
      })}
      {rating > 0 && <span className="text-gray-400 text-sm ml-2">{rating} of 5</span>}
    </div>
  );
};

StarRating.propTypes = {
  rating: PropTypes.number.isRequired
};

const UserProfilePage = () => {
  const { userId } = useParams();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('reviews');
  const [userProfile, setUserProfile] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        // Fetch user profile data
        const profileData = await getProfileFn(userId);
        setUserProfile(profileData);
        
        // Fetch user reviews
        const userReviews = await getReviewsFn(userId);
        setReviews(userReviews);
        
        // Fetch user posts
        const userPosts = await getPostsFn(userId);
        setPosts(userPosts);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError(err.message || 'Failed to load user profile');
        setLoading(false);
      }
    };
    
    fetchUserData();
  }, [userId]);

  const ReviewItem = ({ review }) => (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-4">
      <div className="flex items-start gap-4 mb-4">
        <img 
          src={review.gameImage || gamePlaceholder} 
          alt={review.gameTitle} 
          className="w-16 h-16 rounded-lg object-cover"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = gamePlaceholder;
          }}
        />
        <div className="flex-1">
          <h3 className="text-orange-400 font-semibold text-lg hover:text-orange-300 transition-colors duration-200">
            <Link to={`/game/${review.gameId}`}>{review.gameTitle}</Link>
          </h3>
          <div className="flex items-center gap-4 mt-2">
            <span className="text-gray-500 text-sm">{review.date}</span>
            <StarRating rating={review.rating} />
          </div>
        </div>
      </div>

      <p className="text-gray-300 leading-relaxed mb-4">{review.content}</p>
      
      <div className="flex items-center gap-6 text-sm">
        <span className="flex items-center gap-2 text-gray-400">
          <FaThumbsUp className="text-green-500" /> {review.likesCount || 0}
        </span>
        <span className="flex items-center gap-2 text-gray-400">
          <FaThumbsDown className="text-red-500" /> {review.dislikesCount || 0}
        </span>
        <span className="flex items-center gap-2 text-gray-400">
          <FaComment className="text-blue-500" /> {review.comments || 0}
        </span>
      </div>
    </div>
  );

  ReviewItem.propTypes = {
    review: PropTypes.shape({
      gameImage: PropTypes.string,
      gameTitle: PropTypes.string.isRequired,
      gameId: PropTypes.number.isRequired,
      date: PropTypes.string.isRequired,
      rating: PropTypes.number.isRequired,
      content: PropTypes.string.isRequired,
      likesCount: PropTypes.number,
      dislikesCount: PropTypes.number,
      comments: PropTypes.number
    }).isRequired
  };

  const PostItem = ({ post }) => (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-4">
      <div className="flex items-start justify-between mb-4">
        <h3 className="text-orange-400 font-semibold text-xl hover:text-orange-300 transition-colors duration-200">
          <Link to={`/community/post/${post.id}`}>{post.title}</Link>
        </h3>
        <span className="text-gray-500 text-sm">{post.date}</span>
      </div>
      
      {post.image && (
        <div className="mb-4">
          <img 
            src={post.image} 
            alt={post.title} 
            className="w-full h-48 object-cover rounded-lg"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = null;
            }}
          />
        </div>
      )}
      
      <p className="text-gray-300 leading-relaxed mb-4">{post.excerpt || post.content.substring(0, 200) + '...'}</p>
      
      <div className="flex items-center gap-6 text-sm">
        <span className="flex items-center gap-2 text-gray-400">
          <FaThumbsUp className="text-green-500" /> {post.likesCount || 0}
        </span>
        <span className="flex items-center gap-2 text-gray-400">
          <FaComment className="text-blue-500" /> {post.commentCount || 0}
        </span>
      </div>
    </div>
  );

  PostItem.propTypes = {
    post: PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      date: PropTypes.string.isRequired,
      image: PropTypes.string,
      excerpt: PropTypes.string,
      content: PropTypes.string.isRequired,
      likesCount: PropTypes.number,
      commentCount: PropTypes.number
    }).isRequired
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-300 text-lg">Loading user profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-white text-2xl font-bold mb-2">Error Loading Profile</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <Link to="/" className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg transition-all duration-200">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-white text-2xl font-bold mb-2">User Not Found</h2>
          <p className="text-gray-300 mb-6">The user you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          <Link to="/" className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg transition-all duration-200">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="bg-gray-800 rounded-lg p-8 border border-gray-700 mb-8">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
            <div className="flex-shrink-0">
              <img
                src={userProfile.profileImage || avatarPlaceholder}
                alt={userProfile.username}
                className="w-32 h-32 rounded-full object-cover border-4 border-orange-500"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = avatarPlaceholder;
                }}
              />
            </div>
            <div className="flex-1 text-center md:text-left">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                <h1 className="text-4xl font-bold text-white">{userProfile.username}</h1>
                {/* Only show report button if viewing someone else's profile */}
                {user && user.id !== parseInt(userId) && (
                  <div className="mt-2 md:mt-0">
                    <ReportButton
                      type="user"
                      targetId={userId}
                      targetTitle={userProfile.username}
                      className="text-sm"
                    />
                  </div>
                )}
              </div>
              {userProfile.title && <h2 className="text-xl text-orange-400 mb-4">{userProfile.title}</h2>}
              
              <div className="flex flex-wrap justify-center md:justify-start gap-8 mb-4">
                <div className="text-center">
                  <span className="block text-2xl font-bold text-orange-500">{reviews.length}</span>
                  <span className="text-gray-400 text-sm">Reviews</span>
                </div>
                <div className="text-center">
                  <span className="block text-2xl font-bold text-orange-500">{posts.length}</span>
                  <span className="text-gray-400 text-sm">Posts</span>
                </div>
                <div className="text-center">
                  <span className="block text-2xl font-bold text-orange-500">{userProfile.memberSince}</span>
                  <span className="text-gray-400 text-sm">Member Since</span>
                </div>
              </div>
              
              {userProfile.bio && <p className="text-gray-300 leading-relaxed">{userProfile.bio}</p>}
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="flex border-b border-gray-700">
            <button 
              className={`flex-1 flex items-center justify-center gap-2 px-6 py-4 font-semibold transition-colors duration-200 ${
                activeTab === 'reviews' 
                  ? 'bg-orange-500 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
              onClick={() => setActiveTab('reviews')}
            >
              <FaGamepad /> Reviews ({reviews.length})
            </button>
            <button 
              className={`flex-1 flex items-center justify-center gap-2 px-6 py-4 font-semibold transition-colors duration-200 ${
                activeTab === 'posts' 
                  ? 'bg-orange-500 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
              onClick={() => setActiveTab('posts')}
            >
              <FaComment /> Community Posts ({posts.length})
            </button>
          </div>
          
          <div className="p-6">
            {activeTab === 'reviews' && (
              <div>
                {reviews.length > 0 ? (
                  <div>
                    {reviews.map(review => (
                      <ReviewItem key={review.id} review={review} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FaGamepad className="text-gray-600 text-6xl mx-auto mb-4" />
                    <p className="text-gray-500 text-lg">This user hasn&apos;t written any reviews yet.</p>
                  </div>
                )}
              </div>
            )}
            
            {activeTab === 'posts' && (
              <div>
                {posts.length > 0 ? (
                  <div>
                    {posts.map(post => (
                      <PostItem key={post.id} post={post} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FaComment className="text-gray-600 text-6xl mx-auto mb-4" />
                    <p className="text-gray-500 text-lg">This user hasn&apos;t created any community posts yet.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;

import { useState, useEffect } from 'react';
import GameCard from '../components/GameCard';
import Sidebar from '../components/Sidebar';
import GameLoader from '../components/GameLoader';
import { FaSortAmountDown, FaTrash } from 'react-icons/fa';
import { useLanguage } from '../context/LanguageContext';
import { useAuth } from '../context/AuthContext';
import { useSidebar } from '../context/SidebarContext';

const RecentlyPlayedPage = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const { isSidebarOpen, toggleSidebar } = useSidebar();

  // Recently played games (stored in localStorage for now)
  const [recentlyPlayed, setRecentlyPlayed] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // Sorting
  const [sortOption, setSortOption] = useState('recent');
  const sortOptions = {
    recent: 'Most Recent',
    oldest: 'Oldest First',
    alphabetical: 'Name (A-Z)',
    playTime: 'Most Played'
  };

  // Load recently played games from localStorage
  useEffect(() => {
    const loadRecentlyPlayed = () => {
      try {
        const stored = localStorage.getItem('recentlyPlayed');
        if (stored) {
          const games = JSON.parse(stored);
          setRecentlyPlayed(games);
        }
      } catch (error) {
        console.error('Error loading recently played games:', error);
      } finally {
        setLoading(false);
      }
    };

    loadRecentlyPlayed();
  }, []);

  // Sort games based on selected option
  const getSortedGames = () => {
    if (!recentlyPlayed.length) return [];
    
    const games = [...recentlyPlayed];
    switch (sortOption) {
      case 'recent':
        return games.sort((a, b) => new Date(b.lastPlayed) - new Date(a.lastPlayed));
      case 'oldest':
        return games.sort((a, b) => new Date(a.lastPlayed) - new Date(b.lastPlayed));
      case 'alphabetical':
        return games.sort((a, b) => a.title.localeCompare(b.title));
      case 'playTime':
        return games.sort((a, b) => (b.playTime || 0) - (a.playTime || 0));
      default:
        return games;
    }
  };

  // Clear all recently played games
  const clearAllRecentlyPlayed = () => {
    localStorage.removeItem('recentlyPlayed');
    setRecentlyPlayed([]);
  };

  if (loading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <div className={`flex-1 ${isSidebarOpen ? '' : 'lg:pl-0'} p-5 flex items-center justify-center`}>
          <GameLoader size="lg" variant="controller" />
        </div>
      </div>
    );
  }

  const sortedGames = getSortedGames();

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
      <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
      
      <div className={`flex-1 ${isSidebarOpen ? '' : 'lg:pl-0'}`}>
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-2">
          <div className="flex gap-4">
            <h1 className="text-3xl font-bold text-white">{t('sidebar.navigation.recentlyPlayed')}</h1>
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              {sortedGames.length > 0 && (
                <>
                  <div className="flex items-center gap-2">
                    <FaSortAmountDown className="text-gray-400" />
                    <select 
                      value={sortOption} 
                      onChange={(e) => setSortOption(e.target.value)}
                      className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500 transition-colors duration-200"
                    >
                      {Object.entries(sortOptions).map(([value, label]) => (
                        <option key={value} value={value}>{label}</option>
                      ))}
                    </select>
                  </div>
                  
                  <button
                    onClick={clearAllRecentlyPlayed}
                    className="flex items-center gap-2 px-3 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"
                  >
                    <FaTrash size={14} />
                    <span>Clear All</span>
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="p-6">
          {!user ? (
            <div className="flex flex-col items-center justify-center py-16">
              <h3 className="text-xl font-bold text-white mb-2">Sign in to track your games</h3>
              <p className="text-gray-400 mb-6 text-center">
                Sign in to automatically track the games you play and access them easily from this page.
              </p>
              <a
                href="/login"
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
              >
                Sign In
              </a>
            </div>
          ) : sortedGames.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <h3 className="text-xl font-bold text-white mb-2">No recently played games</h3>
              <p className="text-gray-400 mb-6 text-center">
                Games you play will appear here for easy access. Start playing some games to build your history!
              </p>
              <a
                href="/"
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
              >
                Browse Games
              </a>
            </div>
          ) : (
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-4">
              {sortedGames.map((game, index) => (
                <div key={`${game.id}-${index}`} className="aspect-[230/130]">
                  <GameCard
                    game={game}
                    size="compact"
                    className="h-full w-full rounded-lg overflow-hidden"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RecentlyPlayedPage;

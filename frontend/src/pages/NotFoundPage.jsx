import { Link } from 'react-router-dom';
import { FaHome } from 'react-icons/fa';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';

const NotFoundPage = () => {
  const { createLanguageLink } = useLanguageNavigation();
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="text-center text-white p-8">
        <h1 className="text-6xl font-bold mb-4">404</h1>
        <h2 className="text-2xl mb-6">Page Not Found</h2>
        <p className="text-gray-400 mb-8">
          The page you are looking for does not exist.
        </p>
        <Link
          to={createLanguageLink('/')}
          className="inline-flex items-center gap-2 px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200 no-underline"
        >
          <FaHome /> Back to Home
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;

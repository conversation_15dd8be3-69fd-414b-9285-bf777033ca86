import { useAuth } from '../../context/AuthContext';
import UserManagement from '../../components/admin/UserManagement';

const AdminUsers = () => {
  const { user } = useAuth();

  // Check if user has permission
  if (!user || !['admin', 'moderator'].includes(user.role)) {
    return (
      <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg">
        Access Denied - Admin or Moderator role required
      </div>
    );
  }

  return <UserManagement />;
};

export default AdminUsers;

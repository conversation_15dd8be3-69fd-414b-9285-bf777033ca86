import { useAuth } from '../../context/AuthContext';
import SystemSettings from '../../components/admin/SystemSettings';

const AdminSettings = () => {
  const { user } = useAuth();

  // Check if user has admin permission
  if (!user || user.role !== 'admin') {
    return (
      <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg">
        Access Denied - Admin role required
      </div>
    );
  }

  return <SystemSettings />;
};

export default AdminSettings;

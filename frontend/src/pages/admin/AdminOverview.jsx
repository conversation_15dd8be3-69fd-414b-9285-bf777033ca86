import { useState, useEffect } from 'react';
import DashboardOverview from '../../components/admin/DashboardOverview';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

const AdminOverview = () => {
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_URL}/moderation/dashboard/stats`, {
          withCredentials: true
        });
        setDashboardStats(response.data);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        setError('Failed to load dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  if (error) {
    return (
      <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg mb-6">
        {error}
      </div>
    );
  }

  return <DashboardOverview stats={dashboardStats} loading={loading} />;
};

export default AdminOverview;

# Nginx Configuration for Language-Specific Routing

This document outlines the required Nginx configuration to support language-specific URLs like `/pt`, `/en`, `/es` for the IndieRepo website.

## Required Nginx Configuration

The Nginx configuration should handle the following URL patterns:
- `indierepo.com/pt/*` - Portuguese routes
- `indierepo.com/en/*` - English routes  
- `indierepo.com/es/*` - Spanish routes
- `indierepo.com/*` - Root routes (will redirect to language-specific)

### Sample Nginx Configuration

```nginx
server {
    listen 80;
    listen [::]:80;
    server_name indierepo.com www.indierepo.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name indierepo.com www.indierepo.com;

    # SSL configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # SSL settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Root directory for frontend files
    root /var/www/indierepo/frontend;
    index index.html;

    # API routes - proxy to backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files from S3 (games, images, etc.)
    location /games/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache static files
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Legacy uploads path
    location /uploads/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache static files
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Static assets (CSS, JS, images from build)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Language-specific routes and fallback to React app
    location / {
        # Try to serve the file directly, otherwise fallback to index.html
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
```

## Key Points

1. **API Routes**: All `/api/*` routes are proxied to the backend server running on port 5000.

2. **Static Files**: Game files and uploads are proxied to the backend which serves them from S3.

3. **Frontend Routes**: All other routes (including language-specific ones) fall back to `index.html`, allowing React Router to handle client-side routing.

4. **Caching**: Static assets are cached for 1 year, while HTML files are not cached to ensure updates are immediately available.

5. **Security Headers**: Basic security headers are added to all responses.

## Testing the Configuration

After updating the Nginx configuration, test the following URLs:
- `https://indierepo.com/` - Should redirect to language-specific URL
- `https://indierepo.com/en/` - Should load the English homepage
- `https://indierepo.com/pt/` - Should load the Portuguese homepage  
- `https://indierepo.com/es/` - Should load the Spanish homepage
- `https://indierepo.com/en/game/123` - Should load the game page in English
- `https://indierepo.com/api/health` - Should return API health status

## Deployment Notes

The current deployment script skips Nginx configuration updates since it's configured manually. If you need to update the Nginx configuration:

1. SSH into the EC2 instance
2. Edit the Nginx configuration file (usually in `/etc/nginx/sites-available/`)
3. Test the configuration: `sudo nginx -t`
4. Reload Nginx: `sudo systemctl reload nginx`

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const cookieParser = require('cookie-parser');
const jwt = require('jsonwebtoken');

// Import configurations and middleware
const logger = require('./config/logger');
const corsConfig = require('./config/cors');
const { unityFileHandler, gzipFileHandler, unityGameRedirectHandler } = require('./middleware/unityMiddleware');

// Import routes
const gameRoutes = require('./routes/gameRoutes');
const authRoutes = require('./routes/authRoutes');
const uploadRoutes = require('./routes/uploadRoutes');
const downloadRoutes = require('./routes/downloadRoutes');
const reviewRoutes = require('./routes/reviewRoutes');
const userRoutes = require('./routes/userRoutes');
const moderationRoutes = require('./routes/moderationRoutes');
const ticketRoutes = require('./routes/ticketRoutes');
const reportRoutes = require('./routes/reportRoutes');
const staticFilesRoutes = require('./routes/staticFiles');

// Create Express app
const app = express();

// CORS configuration
app.use(cors(corsConfig));

// Middleware
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Unity file handling middleware
app.use(unityFileHandler);

// Unity game redirect handlers
app.use(unityGameRedirectHandler('7', '2dplatformerweb'));

// Static file middleware for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// General gzipped file handling
app.use(gzipFileHandler);

// Request logging middleware
app.use((req, res, next) => {
  // Only log API routes in development mode and exclude common auth checks
  if (process.env.NODE_ENV === 'development' && 
      req.path.startsWith('/api/') && 
      !req.path.includes('/auth/status')) {
    logger.http(`${req.method} ${req.path}`);
  }
  next();
});

// Routes
app.use('/api/games', gameRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/downloads', downloadRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/users', userRoutes);
app.use('/api/moderation', moderationRoutes);
app.use('/api/tickets', ticketRoutes);
app.use('/api/reports', reportRoutes);

// Static file routes - serve S3 files through main domain
// This enables URLs like: https://indierepo.com/games/1/images/file.jpg
app.use('/', staticFilesRoutes);

// Health check endpoint for the API
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'API is running' });
});

// Add an authentication status endpoint
app.get('/api/auth/status', (req, res) => {
  const token = req.cookies.token;
  
  if (!token) {
    return res.json({ authenticated: false });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    // Only log successful auth in development mode
    if (process.env.NODE_ENV === 'development') {
      logger.debug(`Auth valid for user ID: ${decoded.id}`);
    }
    
    // Send minimal user info
    res.json({ 
      authenticated: true, 
      user: { 
        id: decoded.id,
        role: decoded.role 
      } 
    });
  } catch (err) {
    logger.error(`Auth token validation failed: ${err.name}`);
    res.json({ authenticated: false, error: err.name });
  }
});

// Basic route
app.get('/', (req, res) => {
  res.send('IndieRepo API is running');
});

// Language detection endpoint (optional - for server-side language detection)
app.get('/api/detect-language', (req, res) => {
  // Get client's preferred language from Accept-Language header
  const acceptLanguage = req.headers['accept-language'];
  const supportedLanguages = ['en', 'es', 'pt'];
  const defaultLanguage = 'en';

  let detectedLanguage = defaultLanguage;

  if (acceptLanguage) {
    // Parse Accept-Language header and find best match
    const languages = acceptLanguage.split(',').map(lang => {
      const [code, quality = '1'] = lang.trim().split(';q=');
      return { code: code.substring(0, 2).toLowerCase(), quality: parseFloat(quality) };
    }).sort((a, b) => b.quality - a.quality);

    // Find first supported language
    for (const lang of languages) {
      if (supportedLanguages.includes(lang.code)) {
        detectedLanguage = lang.code;
        break;
      }
    }
  }

  res.json({
    detectedLanguage,
    supportedLanguages,
    acceptLanguage
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Server error:', {
    message: err.message,
    stack: err.stack,
    method: req.method,
    url: req.url,
    body: req.body
  });
  
  res.status(500).json({
    message: 'Something broke!', 
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

module.exports = app;

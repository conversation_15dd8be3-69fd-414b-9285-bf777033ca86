const express = require('express');
const router = express.Router();
const ticketController = require('../controllers/ticketController');
const { authenticateToken } = require('../middleware/authMiddleware');
const { requirePermission, checkBanStatus } = require('../middleware/roleMiddleware');

// Apply authentication and ban check to all routes
router.use(authenticateToken);
router.use(checkBanStatus);

// Create new ticket (any authenticated user)
router.post('/', ticketController.createTicket);

// Get tickets (filtered by role)
router.get('/', ticketController.getTickets);

// Get ticket by ID
router.get('/:ticketId', ticketController.getTicketById);

// Add response to ticket
router.post('/:ticketId/responses', ticketController.addResponse);

// Update ticket (staff only)
router.put('/:ticketId', 
  requirePermission('ASSIGN_TICKETS'), 
  ticketController.updateTicket
);

// Assign ticket (staff only)
router.post('/:ticketId/assign', 
  requirePermission('ASSIGN_TICKETS'), 
  ticketController.assignTicket
);

// Close ticket (staff only)
router.post('/:ticketId/close', 
  requirePermission('RESOLVE_TICKETS'), 
  ticketController.closeTicket
);

// Get ticket statistics (staff only)
router.get('/stats/overview', 
  requirePermission('VIEW_ANALYTICS'), 
  ticketController.getTicketStats
);

module.exports = router;

const express = require('express');
const router = express.Router();
const userController = require('../controllers/user.controller');
const { authenticateToken, optionalAuth } = require('../middleware/authMiddleware');

// Log all requests to this router
router.use((req, res, next) => {

  next();
});

// Profile routes - require authentication
router.get('/profile', authenticateToken, (req, res, next) => {

  next();
}, userController.getProfile);

router.put('/profile', authenticateToken, (req, res, next) => {
  
  next();
}, userController.updateProfile);

// Avatar selection routes
router.put('/select-avatar', authenticateToken, userController.selectAvatar);
router.get('/available-avatars', authenticateToken, userController.getAvailableAvatars);

// Profile image upload route
router.post('/upload-profile-image', authenticateToken, userController.uploadProfileImage);

// User game data routes
router.get('/recently-played', authenticateToken, userController.getRecentlyPlayed);
router.get('/favorites', authenticateToken, userController.getFavorites);
router.get('/debug-user-games', authenticateToken, userController.debugUserGames);

// Public user profile routes
router.get('/:userId/profile', optionalAuth, (req, res, next) => {

  next();
}, userController.getPublicProfile);

router.get('/:userId/reviews', userController.getUserReviews);
router.get('/:userId/posts', userController.getUserPosts);

module.exports = router;

const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');
const { authenticateToken } = require('../middleware/authMiddleware');
const { requirePermission, checkBanStatus } = require('../middleware/roleMiddleware');

// Apply authentication and ban check to all routes
router.use(authenticateToken);
router.use(checkBanStatus);

// Create new report (any authenticated user)
router.post('/', reportController.createReport);

// Get user's own reports
router.get('/my-reports', reportController.getUserReports);

// Staff-only routes
router.get('/', 
  requirePermission('VIEW_REPORTS'), 
  reportController.getReports
);

router.get('/stats', 
  requirePermission('VIEW_REPORTS'), 
  reportController.getReportStats
);

router.get('/:reportId', 
  requirePermission('VIEW_REPORTS'), 
  reportController.getReportById
);

router.post('/:reportId/resolve', 
  requirePermission('RESOLVE_REPORTS'), 
  reportController.resolveReport
);

router.post('/:reportId/dismiss', 
  requirePermission('DISMISS_REPORTS'), 
  reportController.dismissReport
);

router.put('/:reportId/status',
  requirePermission('VIEW_REPORTS'),
  reportController.updateReportStatus
);

// Edit report (admin only)
router.put('/:reportId',
  requirePermission('EDIT_REPORTS'),
  reportController.updateReport
);

// Delete report (admin only)
router.delete('/:reportId',
  requirePermission('DELETE_REPORTS'),
  reportController.deleteReport
);

module.exports = router;

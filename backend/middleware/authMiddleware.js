const jwt = require('jsonwebtoken');
const db = require('../config/database');
const { checkBanStatus } = require('./roleMiddleware');
const logger = require('../config/logger');

// Middleware to authenticate token
exports.authenticateToken = async (req, res, next) => {
  // Get token from cookie instead of Authorization header
  const token = req.cookies.token;
  
  if (!token) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // Handle both old and new token structures
    const userId = decoded.userId || decoded.id;
    
    // Query database to get current user data using Knex
    const user = await db('users')
      .select('id', 'username', 'email', 'role')
      .where('id', userId)
      .first();

    if (!user) {
      return res.status(403).json({ message: 'Invalid token - user not found' });
    }

    // Add user data to request - keeping the same structure for compatibility
    req.user = {
      userId: user.id,
      id: user.id, // Keep for backward compatibility
      username: user.username,
      email: user.email,
      role: user.role
    };
    next();
  } catch (err) {
    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        message: 'Token expired', 
        expired: true 
      });
    }
    
    return res.status(403).json({ message: 'Invalid token' });
  }
};

// Optional auth middleware - will authenticate if token exists, but won't require it
exports.optionalAuth = async (req, res, next) => {
  // Get token from cookie instead of Authorization header
  const token = req.cookies.token;
  
  // If no token, just continue with no user data
  if (!token) {
    req.user = null;
    return next();
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // Handle both old and new token structures
    const userId = decoded.userId || decoded.id;
    
    // Query database to get current user data using Knex
    const user = await db('users')
      .select('id', 'username', 'email', 'role')
      .where('id', userId)
      .first();

    // Add user data to request if found - keeping the same structure for compatibility
    req.user = user ? {
      userId: user.id,
      id: user.id, // Keep for backward compatibility
      username: user.username,
      email: user.email,
      role: user.role
    } : null;
    next();
  } catch (err) {
    // If token validation fails, continue but with no user data
    req.user = null;
    next();
  }
};

// Admin authentication middleware
exports.requireAdmin = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }
  next();
};

// Developer or admin authentication middleware
exports.requireDeveloperOrAdmin = (req, res, next) => {
  if (!req.user || (req.user.role !== 'developer' && req.user.role !== 'admin')) {
    return res.status(403).json({ message: 'Developer or admin access required' });
  }
  next();
};

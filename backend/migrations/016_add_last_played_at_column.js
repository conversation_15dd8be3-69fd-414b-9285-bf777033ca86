/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Add last_played_at column to user_games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (userGamesExists) {
    const hasColumn = await knex.schema.hasColumn('user_games', 'last_played_at');
    if (!hasColumn) {
      await knex.schema.table('user_games', (table) => {
        table.timestamp('last_played_at').nullable();
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Remove last_played_at column from user_games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (userGamesExists) {
    const hasColumn = await knex.schema.hasColumn('user_games', 'last_played_at');
    if (hasColumn) {
      await knex.schema.table('user_games', (table) => {
        table.dropColumn('last_played_at');
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('reports', (table) => {
    table.increments('id').primary();
    table.integer('reporter_id').unsigned().notNullable(); // User who made the report
    table.integer('reported_user_id').unsigned().nullable(); // User being reported (if applicable)
    table.integer('reported_game_id').unsigned().nullable(); // Game being reported (if applicable)
    table.integer('reported_review_id').unsigned().nullable(); // Review being reported (if applicable)
    table.enum('report_type', [
      'offensive_content',
      'sexually_explicit',
      'harassment',
      'spam',
      'copyright_violation',
      'inappropriate_content',
      'rule_violation',
      'fake_content',
      'other'
    ]).notNullable();
    table.text('description').notNullable();
    table.enum('status', ['pending', 'under_review', 'resolved', 'dismissed']).defaultTo('pending');
    table.integer('reviewed_by').unsigned().nullable(); // Admin/Moderator who reviewed
    table.text('resolution_notes').nullable();
    table.timestamp('resolved_at').nullable();
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('reporter_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('reported_user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('reported_game_id').references('id').inTable('games').onDelete('CASCADE');
    table.foreign('reported_review_id').references('id').inTable('reviews').onDelete('CASCADE');
    table.foreign('reviewed_by').references('id').inTable('users').onDelete('SET NULL');
    
    // Add indexes for better performance
    table.index(['reporter_id']);
    table.index(['reported_user_id']);
    table.index(['reported_game_id']);
    table.index(['reported_review_id']);
    table.index(['report_type']);
    table.index(['status']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('reports');
};

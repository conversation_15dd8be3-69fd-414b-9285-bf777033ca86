/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('tickets', (table) => {
    table.increments('id').primary();
    table.integer('user_id').unsigned().notNullable();
    table.integer('assigned_to').unsigned().nullable(); // Admin/Moderator assigned to ticket
    table.string('title', 255).notNullable();
    table.text('description').notNullable();
    table.enum('type', [
      'bug_report',
      'game_issue', 
      'upload_problem',
      'account_issue',
      'content_report',
      'feature_request',
      'other'
    ]).notNullable();
    table.enum('priority', ['low', 'medium', 'high', 'urgent']).defaultTo('medium');
    table.enum('status', ['open', 'in_progress', 'resolved', 'closed']).defaultTo('open');
    table.string('contact_email', 255).nullable(); // Optional contact email
    table.integer('related_game_id').unsigned().nullable(); // If ticket is about a specific game
    table.integer('related_review_id').unsigned().nullable(); // If ticket is about a specific review
    table.integer('related_user_id').unsigned().nullable(); // If ticket is about a specific user
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('assigned_to').references('id').inTable('users').onDelete('SET NULL');
    table.foreign('related_game_id').references('id').inTable('games').onDelete('SET NULL');
    table.foreign('related_review_id').references('id').inTable('reviews').onDelete('SET NULL');
    table.foreign('related_user_id').references('id').inTable('users').onDelete('SET NULL');
    
    // Add indexes for better performance
    table.index(['user_id']);
    table.index(['assigned_to']);
    table.index(['status']);
    table.index(['type']);
    table.index(['priority']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('tickets');
};

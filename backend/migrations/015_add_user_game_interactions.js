/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Add user_liked and user_disliked columns to user_games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (userGamesExists) {
    await knex.schema.table('user_games', (table) => {
      // Check if columns don't already exist
      table.boolean('user_liked').defaultTo(false);
      table.boolean('user_disliked').defaultTo(false);
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Remove user_liked and user_disliked columns from user_games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (userGamesExists) {
    await knex.schema.table('user_games', (table) => {
      table.dropColumn('user_liked');
      table.dropColumn('user_disliked');
    });
  }
};

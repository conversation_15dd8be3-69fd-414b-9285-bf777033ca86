/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Add bio column to users table if it doesn't exist
  const usersExists = await knex.schema.hasTable('users');
  if (usersExists) {
    const hasBio = await knex.schema.hasColumn('users', 'bio');
    if (!hasBio) {
      await knex.schema.table('users', (table) => {
        table.text('bio').nullable();
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Remove bio column from users table
  const usersExists = await knex.schema.hasTable('users');
  if (usersExists) {
    const hasBio = await knex.schema.hasColumn('users', 'bio');
    if (hasBio) {
      await knex.schema.table('users', (table) => {
        table.dropColumn('bio');
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Add user interaction columns to user_games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (userGamesExists) {
    const hasUserLiked = await knex.schema.hasColumn('user_games', 'user_liked');
    if (!hasUserLiked) {
      await knex.schema.table('user_games', (table) => {
        table.boolean('user_liked').defaultTo(false);
        table.boolean('user_disliked').defaultTo(false);
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Remove user interaction columns from user_games table
  const userGamesExists = await knex.schema.hasTable('user_games');
  if (userGamesExists) {
    const hasUserLiked = await knex.schema.hasColumn('user_games', 'user_liked');
    if (hasUserLiked) {
      await knex.schema.table('user_games', (table) => {
        table.dropColumn('user_liked');
        table.dropColumn('user_disliked');
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.alterTable('users', (table) => {
    // Check if columns exist before adding them
    table.boolean('is_banned').defaultTo(false);
    table.timestamp('banned_until').nullable();
    table.text('ban_reason').nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.alterTable('users', (table) => {
    table.dropColumn('is_banned');
    table.dropColumn('banned_until');
    table.dropColumn('ban_reason');
  });
};

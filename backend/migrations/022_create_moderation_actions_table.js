/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('moderation_actions', (table) => {
    table.increments('id').primary();
    table.integer('moderator_id').unsigned().notNullable(); // Admin/Moderator who performed action
    table.integer('target_user_id').unsigned().nullable(); // User affected by action
    table.integer('target_game_id').unsigned().nullable(); // Game affected by action
    table.integer('target_review_id').unsigned().nullable(); // Review affected by action
    table.integer('related_report_id').unsigned().nullable(); // Related report if applicable
    table.enum('action_type', [
      'ban_user',
      'unban_user',
      'delete_game',
      'restore_game',
      'delete_review',
      'edit_review',
      'restore_review',
      'promote_user',
      'demote_user',
      'resolve_report',
      'dismiss_report',
      'other'
    ]).notNullable();
    table.text('reason').notNullable();
    table.json('action_details').nullable(); // Store additional action-specific data
    table.timestamp('expires_at').nullable(); // For temporary actions like bans
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('moderator_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('target_user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('target_game_id').references('id').inTable('games').onDelete('CASCADE');
    table.foreign('target_review_id').references('id').inTable('reviews').onDelete('CASCADE');
    table.foreign('related_report_id').references('id').inTable('reports').onDelete('SET NULL');
    
    // Add indexes for better performance
    table.index(['moderator_id']);
    table.index(['target_user_id']);
    table.index(['target_game_id']);
    table.index(['target_review_id']);
    table.index(['action_type']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('moderation_actions');
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.alterTable('games', (table) => {
    table.string('contact_email', 255).nullable().comment('Developer contact email for bug reports and support');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.alterTable('games', (table) => {
    table.dropColumn('contact_email');
  });
};

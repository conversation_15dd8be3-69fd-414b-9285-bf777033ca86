/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.alterTable('reports', (table) => {
    table.integer('related_ticket_id').unsigned().nullable();
    table.foreign('related_ticket_id').references('id').inTable('tickets').onDelete('SET NULL');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.alterTable('reports', (table) => {
    table.dropForeign(['related_ticket_id']);
    table.dropColumn('related_ticket_id');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('ticket_responses', (table) => {
    table.increments('id').primary();
    table.integer('ticket_id').unsigned().notNullable();
    table.integer('user_id').unsigned().notNullable();
    table.text('message').notNullable();
    table.boolean('is_internal').defaultTo(false); // Internal notes only visible to staff
    table.boolean('is_system').defaultTo(false); // System-generated messages
    table.timestamps(true, true);

    // Foreign key constraints
    table.foreign('ticket_id').references('id').inTable('tickets').onDelete('CASCADE');
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Add indexes for better performance
    table.index(['ticket_id']);
    table.index(['user_id']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('ticket_responses');
};

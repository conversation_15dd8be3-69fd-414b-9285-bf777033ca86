# Environment Configuration
# Production environment settings for AWS EC2 deployment

# Server Configuration
PORT=5000
NODE_ENV=production

# Database Configuration - AWS RDS Connection
DB_HOST=indierepo.cy544o8qq9ug.us-east-1.rds.amazonaws.com
DB_USER=postgres
DB_PASSWORD=JgrK3L2H.UQK%jFu
DB_NAME=indierepo
DB_PORT=5432

# JWT Configuration - Use a strong production secret
JWT_SECRET=klsjdnfksjdnfksjbksdjbfjkhb
JWT_EXPIRES_IN=24h

# OAuth Configuration - Keep the same client IDs but update redirects
GOOGLE_CLIENT_ID=383267241104-v4a33murq9oc29jh4p92gf7po1svjtal.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
DISCORD_CLIENT_ID=1386998123812884580
DISCORD_CLIENT_SECRET=ZwFuj3zvZ7P_7B7yhMbUkqFYyCIVqqgi
# Update with your EC2 instance domain or IP
DISCORD_REDIRECT_URI=https://indierepo.com/auth/discord/callback

# Domain Configuration
ROOT_DOMAIN=indierepo.com
DOMAIN=indierepo.com
FRONTEND_URL=https://indierepo.com
BACKEND_URL=https://indierepo.com/api
APP_PORT=5000

# Debug Flags
DEBUG_UNITY=false

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=mW1AUryTFndcpY+6ZaIDWwG/9kGh1irAl2ZNvHAH
AWS_S3_BUCKET_NAME=indierepo

# File Storage Configuration
STORAGE_TYPE=s3
# Use direct S3 URLs for serving files
UPLOADS_BASE_URL=https://indierepo.s3.us-east-1.amazonaws.com

# Enable SSL for RDS connections
DB_SSL=true

EMAIL_SERVICE=smtp
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=obfjmvpvqeiccxor
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
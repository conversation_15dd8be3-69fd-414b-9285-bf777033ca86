const TicketService = require('../services/ticket.service');
const { hasPermission } = require('../middleware/roleMiddleware');

/**
 * Create a new ticket
 */
exports.createTicket = async (req, res) => {
  try {
    const {
      title,
      description,
      type,
      priority,
      contactEmail,
      relatedGameId,
      relatedReviewId,
      relatedUserId
    } = req.body;
    const userId = req.user.userId;

    // Validate required fields
    if (!title || !description || !type) {
      return res.status(400).json({ 
        message: 'Title, description, and type are required' 
      });
    }

    // Validate type
    const validTypes = [
      'bug_report', 'game_issue', 'upload_problem', 
      'account_issue', 'content_report', 'feature_request', 'other'
    ];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ message: 'Invalid ticket type' });
    }

    const ticketData = {
      userId,
      title: title.trim(),
      description: description.trim(),
      type,
      priority: priority || 'medium',
      contactEmail,
      relatedGameId,
      relatedReviewId,
      relatedUserId
    };

    const ticket = await TicketService.createTicket(ticketData);
    res.status(201).json({ message: 'Ticket created successfully', ticket });
  } catch (error) {
    console.error('Error creating ticket:', error);
    res.status(500).json({ message: 'Server error while creating ticket' });
  }
};

/**
 * Get tickets (filtered by user role)
 */
exports.getTickets = async (req, res) => {
  try {
    const {
      status,
      type,
      priority,
      assignedTo,
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const userRole = req.user.role || 'user';
    const userId = req.user.userId;

    const filters = {};
    const options = {
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      sortBy,
      sortOrder
    };

    // Regular users can only see their own tickets
    if (!hasPermission(userRole, 'VIEW_ALL_TICKETS')) {
      filters.userId = userId;
    } else {
      // Admins/moderators can filter by various criteria
      if (status) filters.status = status;
      if (type) filters.type = type;
      if (priority) filters.priority = priority;
      if (assignedTo) filters.assignedTo = assignedTo;
    }

    const tickets = await TicketService.getTickets(filters, options);

    // Get total count for pagination
    const totalCount = await TicketService.getTicketsCount(filters);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    res.json({
      tickets,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages,
      total: totalCount
    });
  } catch (error) {
    console.error('Error getting tickets:', error);
    res.status(500).json({ message: 'Server error while fetching tickets' });
  }
};

/**
 * Get ticket by ID
 */
exports.getTicketById = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const userRole = req.user.role || 'user';
    const userId = req.user.userId;

    const ticket = await TicketService.getTicketById(ticketId);

    // Check if user can view this ticket
    const canViewAllTickets = hasPermission(userRole, 'VIEW_ALL_TICKETS');
    if (!canViewAllTickets && ticket.user_id !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Filter internal responses for regular users
    if (!hasPermission(userRole, 'VIEW_INTERNAL_NOTES')) {
      ticket.responses = ticket.responses.filter(response => !response.is_internal);
    }

    res.json(ticket);
  } catch (error) {
    console.error('Error getting ticket:', error);
    if (error.message === 'Ticket not found') {
      res.status(404).json({ message: 'Ticket not found' });
    } else {
      res.status(500).json({ message: 'Server error while fetching ticket' });
    }
  }
};

/**
 * Add response to ticket
 */
exports.addResponse = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { message, isInternal } = req.body;
    const userId = req.user.userId;
    const userRole = req.user.role || 'user';

    if (!message || message.trim().length === 0) {
      return res.status(400).json({ message: 'Response message is required' });
    }

    // Check if user can respond to this ticket
    const ticket = await TicketService.getTicketById(ticketId);
    const canViewAllTickets = hasPermission(userRole, 'VIEW_ALL_TICKETS');
    
    if (!canViewAllTickets && ticket.user_id !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Only staff can add internal responses
    const canAddInternal = hasPermission(userRole, 'VIEW_INTERNAL_NOTES');
    const responseData = {
      userId,
      message: message.trim(),
      isInternal: isInternal && canAddInternal
    };

    const response = await TicketService.addResponse(ticketId, responseData);
    res.status(201).json({ message: 'Response added successfully', response });
  } catch (error) {
    console.error('Error adding response:', error);
    res.status(500).json({ message: 'Server error while adding response' });
  }
};

/**
 * Update ticket (assign, change status, etc.)
 */
exports.updateTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { status, priority, assignedTo } = req.body;
    const userRole = req.user.role || 'user';

    // Check permissions
    if (!hasPermission(userRole, 'ASSIGN_TICKETS')) {
      return res.status(403).json({ message: 'Insufficient permissions' });
    }

    const updateData = {};
    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;
    if (assignedTo !== undefined) updateData.assignedTo = assignedTo;

    const updatedTicket = await TicketService.updateTicket(ticketId, updateData);
    res.json({ message: 'Ticket updated successfully', ticket: updatedTicket });
  } catch (error) {
    console.error('Error updating ticket:', error);
    res.status(500).json({ message: 'Server error while updating ticket' });
  }
};

/**
 * Assign ticket
 */
exports.assignTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { assignedTo } = req.body;
    const assignedBy = req.user.userId;

    const ticket = await TicketService.assignTicket(ticketId, assignedTo, assignedBy);
    res.json({ message: 'Ticket assigned successfully', ticket });
  } catch (error) {
    console.error('Error assigning ticket:', error);
    res.status(500).json({ message: 'Server error while assigning ticket' });
  }
};

/**
 * Close ticket
 */
exports.closeTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { reason } = req.body;
    const closedBy = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Closure reason is required' });
    }

    const ticket = await TicketService.closeTicket(ticketId, closedBy, reason);
    res.json({ message: 'Ticket closed successfully', ticket });
  } catch (error) {
    console.error('Error closing ticket:', error);
    res.status(500).json({ message: 'Server error while closing ticket' });
  }
};

/**
 * Get ticket statistics
 */
exports.getTicketStats = async (req, res) => {
  try {
    const { dateFrom, dateTo } = req.query;
    
    const filters = {};
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;

    const stats = await TicketService.getTicketStats(filters);
    res.json(stats);
  } catch (error) {
    console.error('Error getting ticket stats:', error);
    res.status(500).json({ message: 'Server error while fetching ticket statistics' });
  }
};

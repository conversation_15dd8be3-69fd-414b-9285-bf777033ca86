const ModerationService = require('../services/moderation.service');
const db = require('../config/database');

/**
 * Ban a user
 */
exports.banUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason, duration } = req.body; // duration in seconds, null for permanent
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Ban reason is required' });
    }

    // Check if target user exists
    const targetUser = await db('users').select('id', 'username', 'role').where('id', userId).first();
    if (!targetUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Prevent banning admins or users with higher/equal role
    if (targetUser.role === 'admin' || 
        (targetUser.role === 'moderator' && req.user.role !== 'admin')) {
      return res.status(403).json({ message: 'Cannot ban users with equal or higher privileges' });
    }

    const result = await ModerationService.banUser(moderatorId, userId, reason, duration);
    res.json(result);
  } catch (error) {
    console.error('Error banning user:', error);
    res.status(500).json({ message: 'Server error while banning user' });
  }
};

/**
 * Unban a user
 */
exports.unbanUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Unban reason is required' });
    }

    const result = await ModerationService.unbanUser(moderatorId, userId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error unbanning user:', error);
    res.status(500).json({ message: 'Server error while unbanning user' });
  }
};

/**
 * Delete a game
 */
exports.deleteGame = async (req, res) => {
  try {
    const { gameId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Deletion reason is required' });
    }

    // Check if game exists
    const game = await db('games').select('id', 'title').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    const result = await ModerationService.deleteGame(moderatorId, gameId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error deleting game:', error);
    res.status(500).json({ message: 'Server error while deleting game' });
  }
};

/**
 * Restore a game
 */
exports.restoreGame = async (req, res) => {
  try {
    const { gameId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Restoration reason is required' });
    }

    const result = await ModerationService.restoreGame(moderatorId, gameId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error restoring game:', error);
    res.status(500).json({ message: 'Server error while restoring game' });
  }
};

/**
 * Get all reviews for moderation with pagination and search
 */
exports.getAllReviews = async (req, res) => {
  try {
    const { page = 1, limit = 20, search } = req.query;
    const offset = (page - 1) * limit;

    let query = db('reviews as r')
      .select(
        'r.id', 'r.user_id', 'r.game_id', 'r.title', 'r.comment', 'r.rating',
        'r.created_at', 'r.updated_at',
        'u.username as user_username',
        'g.title as game_title'
      )
      .leftJoin('users as u', 'r.user_id', 'u.id')
      .leftJoin('games as g', 'r.game_id', 'g.id')
      .orderBy('r.created_at', 'desc');

    // Add search functionality
    if (search && search.trim()) {
      const searchTerm = `%${search.trim()}%`;
      query = query.where(function() {
        this.where('r.comment', 'ilike', searchTerm)
          .orWhere('u.username', 'ilike', searchTerm)
          .orWhere('g.title', 'ilike', searchTerm);
      });
    }

    // Get total count for pagination
    const totalQuery = query.clone().clearSelect().clearOrder().count('r.id as count').first();
    const total = await totalQuery;
    const totalCount = parseInt(total.count);
    const totalPages = Math.ceil(totalCount / limit);

    // Get paginated results
    const reviews = await query.limit(limit).offset(offset);

    res.json({
      reviews,
      currentPage: parseInt(page),
      totalPages,
      totalCount,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    });
  } catch (error) {
    console.error('Error getting all reviews:', error);
    res.status(500).json({ message: 'Server error while fetching reviews' });
  }
};

/**
 * Get a review for moderation
 */
exports.getReview = async (req, res) => {
  try {
    const { reviewId } = req.params;

    const review = await db('reviews as r')
      .select(
        'r.id', 'r.user_id', 'r.game_id', 'r.title', 'r.comment', 'r.rating',
        'r.created_at', 'r.updated_at',
        'u.username as user_username',
        'g.title as game_title'
      )
      .leftJoin('users as u', 'r.user_id', 'u.id')
      .leftJoin('games as g', 'r.game_id', 'g.id')
      .where('r.id', reviewId)
      .first();

    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    res.json(review);
  } catch (error) {
    console.error('Error getting review:', error);
    res.status(500).json({ message: 'Server error while fetching review' });
  }
};

/**
 * Delete a review
 */
exports.deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Deletion reason is required' });
    }

    // Check if review exists
    const review = await db('reviews').select('id', 'title').where('id', reviewId).first();
    if (!review) {
      return res.status(404).json({ message: 'Review not found' });
    }

    const result = await ModerationService.deleteReview(moderatorId, reviewId, reason);
    res.json(result);
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({ message: 'Server error while deleting review' });
  }
};

/**
 * Edit a review (admin only)
 */
exports.editReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { newContent, reason } = req.body;
    const moderatorId = req.user.userId;

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Edit reason is required' });
    }

    if (!newContent || Object.keys(newContent).length === 0) {
      return res.status(400).json({ message: 'New content is required' });
    }

    const result = await ModerationService.editReview(moderatorId, reviewId, newContent, reason);
    res.json(result);
  } catch (error) {
    console.error('Error editing review:', error);
    res.status(500).json({ message: 'Server error while editing review' });
  }
};

/**
 * Promote user role (admin only)
 */
exports.promoteUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { newRole, reason } = req.body;
    const adminId = req.user.userId;

    if (!['user', 'moderator', 'admin'].includes(newRole)) {
      return res.status(400).json({ message: 'Invalid role specified' });
    }

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({ message: 'Promotion reason is required' });
    }

    const result = await ModerationService.promoteUser(adminId, userId, newRole, reason);
    res.json(result);
  } catch (error) {
    console.error('Error promoting user:', error);
    res.status(500).json({ message: 'Server error while promoting user' });
  }
};

/**
 * Get moderation logs
 */
exports.getModerationLogs = async (req, res) => {
  try {
    const {
      moderatorId,
      actionType,
      targetUserId,
      dateFrom,
      dateTo,
      page = 1,
      limit = 50
    } = req.query;

    const filters = {};
    if (moderatorId) filters.moderatorId = moderatorId;
    if (actionType) filters.actionType = actionType;
    if (targetUserId) filters.targetUserId = targetUserId;
    if (dateFrom) filters.dateFrom = dateFrom;
    if (dateTo) filters.dateTo = dateTo;

    const options = {
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    };

    const rawLogs = await ModerationService.getModerationLogs(filters, options);

    // Get total count for pagination
    const totalCount = await ModerationService.getModerationLogsCount(filters);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    // Transform the logs to match frontend expectations
    const logs = rawLogs.map(log => {
      // Determine target type and details
      let target_type = 'Unknown';
      let target_id = 'N/A';
      let target_details = 'No details available';

      if (log.target_user_id) {
        target_type = 'User';
        target_id = log.target_user_id;
        target_details = log.target_username || `User ID: ${log.target_user_id}`;
      } else if (log.target_game_id) {
        target_type = 'Game';
        target_id = log.target_game_id;
        target_details = log.target_game_title || `Game ID: ${log.target_game_id}`;
      } else if (log.target_review_id) {
        target_type = 'Review';
        target_id = log.target_review_id;
        target_details = `Review ID: ${log.target_review_id}`;
      }

      return {
        id: log.id,
        action: log.action_type,
        moderator: {
          username: log.moderator_username
        },
        target_type,
        target_id,
        target_details,
        reason: log.reason,
        created_at: log.created_at
      };
    });

    res.json({
      logs,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages,
      totalCount
    });
  } catch (error) {
    console.error('Error getting moderation logs:', error);
    res.status(500).json({ message: 'Server error while fetching moderation logs' });
  }
};

/**
 * Get user ban status
 */
exports.getUserBanStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const banStatus = await ModerationService.getUserBanStatus(userId);
    res.json(banStatus);
  } catch (error) {
    console.error('Error getting user ban status:', error);
    res.status(500).json({ message: 'Server error while fetching ban status' });
  }
};

/**
 * Get moderation dashboard stats
 */
exports.getDashboardStats = async (req, res) => {
  try {
    const [
      totalUsers,
      bannedUsers,
      totalGames,
      removedGames,
      totalReviews,
      openTickets,
      pendingReports
    ] = await Promise.all([
      db('users').count('id as count').first(),
      db('users').where('is_banned', true).count('id as count').first(),
      db('games').count('id as count').first(),
      db('games').where('status', 'removed').count('id as count').first(),
      db('reviews').count('id as count').first(),
      db('tickets').whereIn('status', ['open', 'in_progress']).count('id as count').first(),
      db('reports').where('status', 'pending').count('id as count').first()
    ]);

    const stats = {
      users: {
        total: parseInt(totalUsers.count),
        banned: parseInt(bannedUsers.count)
      },
      games: {
        total: parseInt(totalGames.count),
        removed: parseInt(removedGames.count)
      },
      reviews: {
        total: parseInt(totalReviews.count)
      },
      tickets: {
        open: parseInt(openTickets.count)
      },
      reports: {
        pending: parseInt(pendingReports.count)
      }
    };

    res.json(stats);
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({ message: 'Server error while fetching dashboard stats' });
  }
};

/**
 * Get users for admin dashboard
 */
exports.getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '', role = 'all', status = 'all' } = req.query;
    const offset = (page - 1) * limit;

    // Build base query for filtering
    let baseQuery = db('users');

    // Apply filters
    if (search) {
      baseQuery = baseQuery.where(function() {
        this.where('username', 'ilike', `%${search}%`)
            .orWhere('email', 'ilike', `%${search}%`);
      });
    }

    if (role !== 'all') {
      baseQuery = baseQuery.where('role', role);
    }

    if (status === 'banned') {
      baseQuery = baseQuery.where('is_banned', true);
    } else if (status === 'active') {
      baseQuery = baseQuery.where('is_banned', false);
    }

    // Get total count for pagination (separate query)
    const totalResult = await baseQuery.clone().count('id as count').first();
    const total = parseInt(totalResult.count);
    const totalPages = Math.ceil(total / limit);

    // Get paginated results (separate query)
    const users = await baseQuery.clone()
      .select('id', 'username', 'email', 'role', 'is_banned', 'ban_reason', 'banned_until', 'created_at')
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    res.json({
      users,
      currentPage: parseInt(page),
      totalPages,
      total
    });
  } catch (error) {
    console.error('Error getting users:', error);
    res.status(500).json({ message: 'Server error while fetching users' });
  }
};

/**
 * Get games for admin dashboard
 */
exports.getGames = async (req, res) => {
  try {
    const { page = 1, limit = 20, search = '', status = 'all' } = req.query;
    const offset = (page - 1) * limit;

    // Build base query for filtering
    let baseQuery = db('games').leftJoin('users', 'games.user_id', 'users.id');

    // Apply filters
    if (search) {
      baseQuery = baseQuery.where('games.title', 'ilike', `%${search}%`);
    }

    if (status !== 'all') {
      baseQuery = baseQuery.where('games.status', status);
    }

    // Get total count for pagination (separate query)
    const totalResult = await baseQuery.clone().count('games.id as count').first();
    const total = parseInt(totalResult.count);
    const totalPages = Math.ceil(total / limit);

    // Get paginated results (separate query)
    const games = await baseQuery.clone()
      .select('games.*', 'users.username as uploader_username')
      .orderBy('games.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    res.json({
      games,
      currentPage: parseInt(page),
      totalPages,
      total
    });
  } catch (error) {
    console.error('Error getting games:', error);
    res.status(500).json({ message: 'Server error while fetching games' });
  }
};

/**
 * Get system settings
 */
exports.getSettings = async (req, res) => {
  try {
    // Return default settings for now
    const settings = {
      siteName: 'IndieRepo',
      siteDescription: 'Discover and play indie games',
      maxFileSize: '1024', // MB
      allowedFileTypes: '.zip',
      moderationMode: 'manual', // manual, auto, hybrid
      emailNotifications: true,
      autoApproveGames: false,
      maintenanceMode: false
    };

    res.json(settings);
  } catch (error) {
    console.error('Error getting settings:', error);
    res.status(500).json({ message: 'Server error while fetching settings' });
  }
};

/**
 * Update system settings
 */
exports.updateSettings = async (req, res) => {
  try {
    const settings = req.body;

    // For now, just return success
    // In a real implementation, you'd save these to a settings table
    console.log('Settings updated:', settings);

    res.json({ message: 'Settings updated successfully', settings });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({ message: 'Server error while updating settings' });
  }
};

/**
 * Debug endpoint to check user role and permissions
 */
exports.debugUserRole = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Get user details from database
    const user = await db('users')
      .select('id', 'username', 'email', 'role', 'is_banned', 'ban_reason', 'banned_until')
      .where('id', userId)
      .first();

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      message: 'User role debug info',
      user: user,
      requestUser: req.user,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    res.status(500).json({ message: 'Server error in debug endpoint' });
  }
};

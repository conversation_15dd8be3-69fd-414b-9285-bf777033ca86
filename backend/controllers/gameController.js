const db = require('../config/database');
const fs = require('fs');
const path = require('path');

/**
 * Helper function to construct proper image URLs
 * @param {string} filePath - The file path from database (could be S3 URL or local path)
 * @param {string} baseUrl - The base URL for local files
 * @returns {string|null} - Properly constructed URL or null
 */
const constructImageUrl = (filePath, baseUrl) => {
  if (!filePath) return null;
  
  // If it's already a full URL (S3 or external), return as is
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }
  
  // For local paths, prepend the base URL
  return baseUrl + filePath;
};

/**
 * Get all games with optional filtering
 */
exports.getAllGames = async (req, res) => {
  try {
    // Get filter parameters
    const { genre, tag, search, sort = 'newest', page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    
    // Build base query using Knex
    let query = db('games as g')
      .select('g.*', 'u.username as publisher_name')
      .join('users as u', 'g.user_id', 'u.id');
    
    // Add WHERE conditions if filters are provided
    if (genre) {
      query = query.where('g.genre', genre);
    }
    
    if (tag) {
      query = query.where('g.tags', 'ilike', `%${tag}%`);
    }

    if (search) {
      query = query.where(function() {
        // Priority 1: Starts with search term (word boundary)
        this.where('g.title', 'ilike', `${search}%`)
            .orWhere('g.description', 'ilike', `${search}%`)
            .orWhere('g.genre', 'ilike', `${search}%`)
            .orWhere('g.tags', 'ilike', `${search}%`)
            // Priority 2: Word starts with search term
            .orWhereRaw('g.title ~* ?', [`\\m${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`])
            .orWhereRaw('g.description ~* ?', [`\\m${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`])
            .orWhereRaw('g.genre ~* ?', [`\\m${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`])
            .orWhereRaw('g.tags ~* ?', [`\\m${search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`])
            // Priority 3: Contains search term (fallback)
            .orWhere('g.title', 'ilike', `%${search}%`)
            .orWhere('g.description', 'ilike', `%${search}%`)
            .orWhere('g.genre', 'ilike', `%${search}%`)
            .orWhere('g.tags', 'ilike', `%${search}%`);
      });
    }
    
    // Add sorting
    switch (sort) {
      case 'newest':
        query = query.orderBy('g.created_at', 'desc');
        break;
      case 'oldest':
        query = query.orderBy('g.created_at', 'asc');
        break;
      case 'popular':
        query = query.orderBy('g.view_count', 'desc');
        break;
      case 'downloads':
        query = query.orderBy('g.download_count', 'desc');
        break;
      case 'alphabetical':
        query = query.orderBy('g.title', 'asc');
        break;
      default:
        query = query.orderBy('g.created_at', 'desc');
    }
    
    // Get total count for pagination
    const countQuery = query.clone().clearSelect().clearOrder().count('* as total');
    const [{ total }] = await countQuery;
    const totalGames = parseInt(total);
    const totalPages = Math.ceil(totalGames / limit);
    
    // Apply pagination to main query
    const games = await query.limit(parseInt(limit)).offset(offset);
    
    // Get additional data for each game
    const gamesWithDetails = await Promise.all(games.map(async (game) => {
      // Get game cover image
      const coverImage = await db('game_images')
        .select('file_path')
        .where('game_id', game.id)
        .where('image_type', 'cover')
        .first();
      
      // Get card image
      const cardImage = await db('game_images')
        .select('file_path')
        .where('game_id', game.id)
        .where('image_type', 'card')
        .first();
      
      // Get animation gif
      const gifImage = await db('game_images')
        .select('file_path')
        .where('game_id', game.id)
        .where('image_type', 'gif')
        .first();
      
      // Get main video URL
      const mainVideo = await db('game_videos')
        .select('video_url')
        .where('game_id', game.id)
        .where('display_order', 0)
        .first();
      
      // Get base URL for image paths
      const baseUrl = process.env.APP_HOST 
        ? `${process.env.APP_HOST}:${process.env.APP_PORT || 3000}`
        : 'http://localhost:3000';
      
      return {
        ...game,
        image: coverImage ? constructImageUrl(coverImage.file_path, baseUrl) : null,
        cardImage: cardImage ? constructImageUrl(cardImage.file_path, baseUrl) : null,
        animationGif: gifImage ? constructImageUrl(gifImage.file_path, baseUrl) : null,
        mainVideo: mainVideo ? mainVideo.video_url : null
      };
    }));
    
    // Return results with pagination info
    res.json({
      games: gamesWithDetails,
      pagination: {
        totalGames,
        totalPages,
        currentPage: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching games:', error);
    res.status(500).json({ message: 'Server error fetching games', error: error.message });
  }
};

/**
 * Get a specific game by ID with all details
 */
exports.getGameById = async (req, res) => {
  const { id } = req.params;

  try {
    // Get the game details
    const game = await db('games as g')
      .select('g.*', 'u.username as publisher_name', 'u.display_name as publisher_display_name')
      .join('users as u', 'g.user_id', 'u.id')
      .where('g.id', id)
      .first();

    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Get game's images
    const images = await db('game_images')
      .where('game_id', id);

    // Get game's screenshots
    const screenshots = await db('game_screenshots')
      .where('game_id', id)
      .orderBy('display_order');

    // Get game's videos
    const videos = await db('game_videos')
      .where('game_id', id)
      .orderBy('display_order');

    // Get game's files
    const files = await db('game_files')
      .where('game_id', id);

    // Get base URL for image paths
    const baseUrl = process.env.APP_HOST 
      ? `${process.env.APP_HOST}:${process.env.APP_PORT || 3000}`
      : '';

    // Format the response
    const gameData = {
      ...game,
      image: constructImageUrl(images.find(img => img.image_type === 'cover')?.file_path, baseUrl),
      cardImage: constructImageUrl(images.find(img => img.image_type === 'card')?.file_path, baseUrl),
      animationGif: constructImageUrl(images.find(img => img.image_type === 'gif')?.file_path, baseUrl),
      screenshots: screenshots.map(s => constructImageUrl(s.file_path, baseUrl)),
      mainVideo: videos.find(v => v.display_order === 0)?.video_url || null,
      additionalVideos: videos.filter(v => v.display_order > 0).map(v => v.video_url),
      files: files.map(file => ({
        id: file.id,
        fileName: file.file_name,
        filePath: file.file_path,
        fileSize: file.file_size,
        fileType: file.file_type,
        downloadUrl: file.download_url,
        notes: file.notes
      })),
      images: images.map(img => ({
        id: img.id,
        imageType: img.image_type,
        filePath: constructImageUrl(img.file_path, baseUrl),
        displayOrder: img.display_order
      })),
      videos: videos.map(video => ({
        id: video.id,
        videoUrl: video.video_url,
        videoType: video.video_type,
        title: video.title,
        description: video.description,
        displayOrder: video.display_order
      }))
    };

    // Increment view count
    await db('games').where('id', id).increment('view_count', 1);

    res.json(gameData);
  } catch (error) {
    console.error('Error fetching game:', error);
    res.status(500).json({ message: 'Server error fetching game', error: error.message });
  }
};

/**
 * Create a new game
 */
exports.createGame = async (req, res) => {
  try {
    const {
      title,
      description,
      genre,
      tags,
      price = 0,
      isFree = true,
      platform = 'web',
      version = '1.0.0'
    } = req.body;

    const userId = req.user.userId;

    // Validate required fields
    if (!title || !description) {
      return res.status(400).json({ 
        message: 'Title and description are required' 
      });
    }

    // Create the game
    const [gameResult] = await db('games').insert({
      user_id: userId,
      title,
      description,
      genre,
      tags: typeof tags === 'string' ? tags : JSON.stringify(tags),
      price: parseFloat(price),
      is_free: isFree,
      platform,
      version,
      status: 'draft'
    }).returning('id');

    // Extract the actual ID from the result object
    const gameId = gameResult.id || gameResult;

    // Get the created game with user info
    const game = await db('games as g')
      .select('g.*', 'u.username as publisher_name')
      .join('users as u', 'g.user_id', 'u.id')
      .where('g.id', gameId)
      .first();

    res.status(201).json({
      message: 'Game created successfully',
      game
    });
  } catch (error) {
    console.error('Error creating game:', error);
    res.status(500).json({ 
      message: 'Server error creating game', 
      error: error.message 
    });
  }
};

/**
 * Update a game
 */
exports.updateGame = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;
    const updateData = req.body;

    // Check if game exists and user owns it
    const game = await db('games')
      .where('id', id)
      .where('user_id', userId)
      .first();

    if (!game) {
      return res.status(404).json({ 
        message: 'Game not found or you do not have permission to update it' 
      });
    }

    // Prepare update fields
    const updateFields = {};
    if (updateData.title) updateFields.title = updateData.title;
    if (updateData.description !== undefined) updateFields.description = updateData.description;
    if (updateData.genre) updateFields.genre = updateData.genre;
    if (updateData.tags !== undefined) {
      updateFields.tags = typeof updateData.tags === 'string' ? updateData.tags : JSON.stringify(updateData.tags);
    }
    if (updateData.price !== undefined) updateFields.price = parseFloat(updateData.price);
    if (updateData.isFree !== undefined) updateFields.is_free = updateData.isFree;
    if (updateData.platform) updateFields.platform = updateData.platform;
    if (updateData.version) updateFields.version = updateData.version;
    if (updateData.status) updateFields.status = updateData.status;

    updateFields.updated_at = new Date();

    // Update the game
    await db('games').where('id', id).update(updateFields);

    // Get the updated game
    const updatedGame = await db('games as g')
      .select('g.*', 'u.username as publisher_name')
      .join('users as u', 'g.user_id', 'u.id')
      .where('g.id', id)
      .first();

    res.json({
      message: 'Game updated successfully',
      game: updatedGame
    });
  } catch (error) {
    console.error('Error updating game:', error);
    res.status(500).json({ 
      message: 'Server error updating game', 
      error: error.message 
    });
  }
};

/**
 * Delete a game
 */
exports.deleteGame = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    // Check if game exists and user owns it
    const game = await db('games')
      .where('id', id)
      .where('user_id', userId)
      .first();

    if (!game) {
      return res.status(404).json({ 
        message: 'Game not found or you do not have permission to delete it' 
      });
    }

    // Delete the game (cascading deletes will handle related records)
    await db('games').where('id', id).del();

    res.json({ message: 'Game deleted successfully' });
  } catch (error) {
    console.error('Error deleting game:', error);
    res.status(500).json({ 
      message: 'Server error deleting game', 
      error: error.message 
    });
  }
};

/**
 * Get games by user ID
 */
exports.getGamesByUserId = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // Get games by user
    const games = await db('games as g')
      .select('g.*', 'u.username as publisher_name')
      .join('users as u', 'g.user_id', 'u.id')
      .where('g.user_id', userId)
      .orderBy('g.created_at', 'desc')
      .limit(parseInt(limit))
      .offset(offset);

    // Get total count
    const [{ total }] = await db('games')
      .where('user_id', userId)
      .count('* as total');
    
    const totalGames = parseInt(total);
    const totalPages = Math.ceil(totalGames / limit);

    res.json({
      games,
      pagination: {
        totalGames,
        totalPages,
        currentPage: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user games:', error);
    res.status(500).json({ 
      message: 'Server error fetching user games', 
      error: error.message 
    });
  }
};

/**
 * Get all unique genres
 */
exports.getGenres = async (req, res) => {
  try {
    const results = await db('games')
      .distinct('genre')
      .whereNotNull('genre')
      .orderBy('genre');
    
    const genres = results.map(result => result.genre);
    res.json(genres);
  } catch (error) {
    console.error('Error fetching genres:', error);
    res.status(500).json({ message: 'Server error fetching genres', error: error.message });
  }
};

/**
 * Get all unique tags
 */
exports.getTags = async (req, res) => {
  try {
    const results = await db('games')
      .select('tags')
      .whereNotNull('tags');
    
    // Extract all tags and remove duplicates
    const allTags = new Set();
    results.forEach(result => {
      if (result.tags) {
        const tags = result.tags.split(',').map(tag => tag.trim());
        tags.forEach(tag => {
          if (tag) allTags.add(tag);
        });
      }
    });
    
    res.json(Array.from(allTags));
  } catch (error) {
    console.error('Error fetching tags:', error);
    res.status(500).json({ message: 'Server error fetching tags', error: error.message });
  }
};

// Get games uploaded by the authenticated user
exports.getUserUploads = async (req, res) => {
  try {
    const userId = req.user.userId;
    console.log('Fetching uploads for user:', userId);

    // Query to get all games uploaded by this user
    const games = await db('games as g')
      .leftJoin('game_images as cover_img', function() {
        this.on('g.id', '=', 'cover_img.game_id')
            .andOn('cover_img.image_type', '=', db.raw('?', ['cover']));
      })
      .leftJoin('game_images as card_img', function() {
        this.on('g.id', '=', 'card_img.game_id')
            .andOn('card_img.image_type', '=', db.raw('?', ['card']));
      })
      .select([
        'g.*',
        'cover_img.file_path as cover_image',
        'card_img.file_path as card_image'
      ])
      .where('g.user_id', userId)
      .orderBy('g.created_at', 'desc');

    console.log('Found games:', games.length);

    // Get additional stats for each game
    const gamesWithStats = await Promise.all(games.map(async (game) => {
      // Get play count from user_games table
      const [playStats] = await db('user_games')
        .where('game_id', game.id)
        .count('* as play_count');

      // Get average rating from reviews
      const [ratingStats] = await db('reviews')
        .where('game_id', game.id)
        .avg('rating as avg_rating')
        .count('* as review_count');

      // Get files count
      const [filesStats] = await db('game_files')
        .where('game_id', game.id)
        .count('* as files_count');

      return {
        ...game,
        play_count: parseInt(playStats.play_count) || 0,
        average_rating: parseFloat(ratingStats.avg_rating) || 0,
        review_count: parseInt(ratingStats.review_count) || 0,
        files_count: parseInt(filesStats.files_count) || 0
      };
    }));

    // Calculate total stats
    const totalPlays = gamesWithStats.reduce((sum, game) => sum + game.play_count, 0);
    const totalAdImpressions = Math.floor(totalPlays * 2.5);
    const totalAdRevenue = totalPlays * 0.02;

    // Format games for frontend
    const formattedGames = gamesWithStats.map(game => ({
      id: game.id,
      title: game.title,
      description: game.description,
      genre: game.genre,
      slug: game.slug, // Include slug for URL generation
      status: game.status || 'published',
      createdAt: game.created_at,
      downloadCount: game.play_count, // Using play count as download count
      averageRating: game.average_rating,
      reviewCount: game.review_count,
      filesCount: game.files_count,
      priceModel: game.price_model || 'free',
      price: game.price || 0,
      creditPrice: game.credit_price || 0,
      coverImage: game.cover_image,
      cardImage: game.card_image,
      // Ad revenue calculations
      adImpressions: Math.floor(game.play_count * 2.5),
      adRevenue: (game.play_count * 0.02).toFixed(2)
    }));

    // Return games array (empty or with data) and total stats
    return res.json({
      games: formattedGames,
      totalRevenue: totalAdRevenue.toFixed(2),
      totalPlays,
      totalAdImpressions,
      totalAdRevenue: totalAdRevenue.toFixed(2)
    });
  } catch (error) {
    console.error('Error getting user uploads:', error);
    res.status(500).json({ message: 'Failed to get user uploads', error: error.message });
  }
};

exports.getGameBySlug = async (req, res) => {
  const { slug } = req.params;

  try {
    // Since slug column doesn't exist, we'll try multiple approaches to find the game
    let game = null;

    // First, try to parse if it's actually a game ID
    const possibleId = parseInt(slug);
    if (!isNaN(possibleId)) {
      game = await db('games as g')
        .select('g.*', 'u.username as publisher_name', 'u.display_name as publisher_display_name')
        .join('users as u', 'g.user_id', 'u.id')
        .where('g.id', possibleId)
        .first();
    }

    // If not found by ID, try to match by title
    if (!game) {
      // Convert slug back to title format for searching
      const titleFromSlug = slug.replace(/-/g, ' ');

      // Try exact match first (case insensitive)
      game = await db('games as g')
        .select('g.*', 'u.username as publisher_name', 'u.display_name as publisher_display_name')
        .join('users as u', 'g.user_id', 'u.id')
        .whereRaw('LOWER(g.title) = LOWER(?)', [titleFromSlug])
        .first();

      // If still not found, try partial match
      if (!game) {
        game = await db('games as g')
          .select('g.*', 'u.username as publisher_name', 'u.display_name as publisher_display_name')
          .join('users as u', 'g.user_id', 'u.id')
          .whereRaw('LOWER(g.title) LIKE LOWER(?)', [`%${titleFromSlug}%`])
          .first();
      }
    }

    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Get game's images
    const images = await db('game_images')
      .where('game_id', game.id);

    // Get game's screenshots
    const screenshots = await db('game_screenshots')
      .where('game_id', game.id)
      .orderBy('display_order');

    // Get game's videos
    const videos = await db('game_videos')
      .where('game_id', game.id)
      .orderBy('display_order');

    // Get game's files
    const files = await db('game_files')
      .where('game_id', game.id);

    // Get base URL for image paths
    const baseUrl = process.env.APP_HOST 
      ? `${process.env.APP_HOST}:${process.env.APP_PORT || 3000}`
      : '';

    // Format the response - identical to getGameById logic
    const gameData = {
      ...game,
      image: constructImageUrl(images.find(img => img.image_type === 'cover')?.file_path, baseUrl),
      cardImage: constructImageUrl(images.find(img => img.image_type === 'card')?.file_path, baseUrl),
      animationGif: constructImageUrl(images.find(img => img.image_type === 'gif')?.file_path, baseUrl),
      screenshots: screenshots.map(s => constructImageUrl(s.file_path, baseUrl)),
      mainVideo: videos.find(v => v.display_order === 0)?.video_url || null,
      additionalVideos: videos.filter(v => v.display_order > 0).map(v => v.video_url),
      files: files.map(file => ({
        id: file.id,
        fileName: file.file_name,
        filePath: file.file_path,
        fileSize: file.file_size,
        fileType: file.file_type,
        downloadUrl: file.download_url,
        notes: file.notes
      }))
    };

    // Increment view count
    await db('games').where('id', game.id).increment('view_count', 1);

    res.json(gameData);
  } catch (error) {
    console.error('Error fetching game by slug:', error);
    res.status(500).json({
      message: 'Failed to fetch game details by slug',
      error: error.message
    });
  }
};

/**
 * Like a game
 */
exports.likeGame = async (req, res) => {
  try {
    const { gameId } = req.params;
    const userId = req.user.userId;

    // Check if game exists
    const game = await db('games').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Check if user already has an interaction with this game
    const existingInteraction = await db('user_games')
      .where({ user_id: userId, game_id: gameId })
      .first();

    if (existingInteraction) {
      // Toggle like status
      const newLikedStatus = !existingInteraction.user_liked;
      await db('user_games')
        .where({ user_id: userId, game_id: gameId })
        .update({
          user_liked: newLikedStatus,
          user_disliked: false, // Remove dislike if liking
          updated_at: new Date()
        });
    } else {
      // Create new interaction
      await db('user_games').insert({
        user_id: userId,
        game_id: gameId,
        user_liked: true,
        user_disliked: false,
        is_favorite: false,
        in_library: false,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // Get updated counts and user status
    const interactions = await getGameInteractionCounts(gameId, userId);
    res.json(interactions);
  } catch (error) {
    console.error('Error liking game:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Dislike a game
 */
exports.dislikeGame = async (req, res) => {
  try {
    const { gameId } = req.params;
    const userId = req.user.userId;

    // Check if game exists
    const game = await db('games').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Check if user already has an interaction with this game
    const existingInteraction = await db('user_games')
      .where({ user_id: userId, game_id: gameId })
      .first();

    if (existingInteraction) {
      // Toggle dislike status
      const newDislikedStatus = !existingInteraction.user_disliked;
      await db('user_games')
        .where({ user_id: userId, game_id: gameId })
        .update({
          user_disliked: newDislikedStatus,
          user_liked: false, // Remove like if disliking
          updated_at: new Date()
        });
    } else {
      // Create new interaction
      await db('user_games').insert({
        user_id: userId,
        game_id: gameId,
        user_liked: false,
        user_disliked: true,
        is_favorite: false,
        in_library: false,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // Get updated counts and user status
    const interactions = await getGameInteractionCounts(gameId, userId);
    res.json(interactions);
  } catch (error) {
    console.error('Error disliking game:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Toggle favorite status for a game
 */
exports.toggleFavorite = async (req, res) => {
  try {
    const { gameId } = req.params;
    const userId = req.user.userId;

    // Check if game exists
    const game = await db('games').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Check if user already has an interaction with this game
    const existingInteraction = await db('user_games')
      .where({ user_id: userId, game_id: gameId })
      .first();

    if (existingInteraction) {
      // Toggle favorite status
      const newFavoriteStatus = !existingInteraction.is_favorite;
      await db('user_games')
        .where({ user_id: userId, game_id: gameId })
        .update({
          is_favorite: newFavoriteStatus,
          updated_at: new Date()
        });
    } else {
      // Create new interaction
      await db('user_games').insert({
        user_id: userId,
        game_id: gameId,
        user_liked: false,
        user_disliked: false,
        is_favorite: true,
        in_library: false,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // Get updated counts and user status
    const interactions = await getGameInteractionCounts(gameId, userId);
    res.json(interactions);
  } catch (error) {
    console.error('Error toggling favorite:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Track when a user plays a game
 */
exports.trackGamePlay = async (req, res) => {
  try {
    const { gameId } = req.params;
    const userId = req.user.userId;

    // Check if game exists
    const game = await db('games').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Check if user already has an interaction with this game
    const existingInteraction = await db('user_games')
      .where({ user_id: userId, game_id: gameId })
      .first();

    if (existingInteraction) {
      // Update last played time
      await db('user_games')
        .where({ user_id: userId, game_id: gameId })
        .update({
          last_played_at: new Date(),
          updated_at: new Date()
        });
    } else {
      // Create new interaction with play tracking
      await db('user_games').insert({
        user_id: userId,
        game_id: gameId,
        user_liked: false,
        user_disliked: false,
        is_favorite: false,
        in_library: false,
        last_played_at: new Date(),
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // Increment game view count
    await db('games')
      .where('id', gameId)
      .increment('view_count', 1);

    res.json({ message: 'Game play tracked successfully' });
  } catch (error) {
    console.error('Error tracking game play:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get game interactions (likes, dislikes, favorites count and user status)
 */
exports.getGameInteractions = async (req, res) => {
  try {
    const { gameId } = req.params;
    const userId = req.user?.userId || null;

    // Check if game exists
    const game = await db('games').where('id', gameId).first();
    if (!game) {
      return res.status(404).json({ message: 'Game not found' });
    }

    // Get interaction counts and user status
    const interactions = await getGameInteractionCounts(gameId, userId);
    res.json(interactions);
  } catch (error) {
    console.error('Error getting game interactions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Helper function to get game interaction counts and user status
 */
async function getGameInteractionCounts(gameId, userId = null) {
  // Get total counts
  const [likesResult] = await db('user_games')
    .where({ game_id: gameId, user_liked: true })
    .count('* as count');

  const [dislikesResult] = await db('user_games')
    .where({ game_id: gameId, user_disliked: true })
    .count('* as count');

  const likes = parseInt(likesResult.count) || 0;
  const dislikes = parseInt(dislikesResult.count) || 0;

  let userLiked = false;
  let userDisliked = false;
  let isFavorite = false;

  // Get user-specific status if user is logged in
  if (userId) {
    const userInteraction = await db('user_games')
      .where({ user_id: userId, game_id: gameId })
      .first();

    if (userInteraction) {
      userLiked = !!userInteraction.user_liked;
      userDisliked = !!userInteraction.user_disliked;
      isFavorite = !!userInteraction.is_favorite;
    }
  }

  return {
    likes,
    dislikes,
    userLiked,
    userDisliked,
    isFavorite
  };
}

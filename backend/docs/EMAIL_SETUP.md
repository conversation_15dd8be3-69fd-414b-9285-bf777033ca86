# Email Service Setup

This document explains how to configure the email service for sending welcome emails and password reset emails.

## Configuration

The email service supports two types of email providers:

### 1. Gmail (Recommended for development)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
3. **Configure environment variables**:
   ```env
   EMAIL_SERVICE=gmail
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your_16_character_app_password
   ```

### 2. Generic SMTP

For other email providers (Send<PERSON>rid, Mailgun, etc.):

```env
EMAIL_SERVICE=smtp
EMAIL_USER=your_smtp_username
EMAIL_PASSWORD=your_smtp_password
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false
```

## Features

### Welcome Email
- Automatically sent after user registration
- Includes personalized greeting with username
- Features overview and call-to-action
- Responsive HTML design with fallback text version

### Password Reset Email
- Sent when user requests password reset
- Includes secure reset link with token
- Link expires after 1 hour
- Clear instructions and security notice

## Testing

The email service will log warnings if not configured and skip sending emails. This allows the application to work without email configuration during development.

To test email functionality:

1. Configure email settings in `.env`
2. Register a new user account
3. Check the configured email inbox for welcome email
4. Check server logs for email sending status

## Troubleshooting

### Gmail Issues
- **"Less secure app access"**: Use App Password instead of regular password
- **Authentication failed**: Verify 2FA is enabled and app password is correct
- **Quota exceeded**: Gmail has daily sending limits for free accounts

### SMTP Issues
- **Connection timeout**: Check SMTP host and port
- **Authentication failed**: Verify username/password
- **TLS/SSL errors**: Check SMTP_SECURE setting (true for port 465, false for 587)

### General Issues
- **Email not received**: Check spam folder
- **Service unavailable**: Check server logs for detailed error messages
- **Configuration missing**: Service will log warnings and skip email sending

## Production Considerations

1. **Use dedicated email service** (SendGrid, Mailgun, AWS SES) instead of Gmail
2. **Configure proper SPF/DKIM records** for better deliverability
3. **Monitor email sending quotas** and error rates
4. **Implement email templates** for different languages if needed
5. **Add unsubscribe functionality** for marketing emails (if added later)

## Email Templates

Email templates are generated programmatically in the `EmailService` class. To customize:

1. Edit `generateWelcomeEmailHTML()` and `generateWelcomeEmailText()` methods
2. Update styling in the HTML template
3. Modify content and messaging as needed
4. Test with different email clients for compatibility

## Security Notes

- Never commit email credentials to version control
- Use environment variables for all sensitive configuration
- Rotate email passwords regularly
- Monitor for suspicious email activity
- Implement rate limiting for email sending if needed

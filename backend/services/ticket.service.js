const db = require('../config/database');
const emailService = require('./emailService');

/**
 * Ticket Service
 * Handles all ticket-related operations
 */
class TicketService {
  
  /**
   * Create a new ticket
   */
  static async createTicket(ticketData) {
    try {
      const [ticketId] = await db('tickets').insert({
        user_id: ticketData.userId,
        title: ticketData.title,
        description: ticketData.description,
        type: ticketData.type,
        priority: ticketData.priority || 'medium',
        contact_email: ticketData.contactEmail,
        related_game_id: ticketData.relatedGameId || null,
        related_review_id: ticketData.relatedReviewId || null,
        related_user_id: ticketData.relatedUserId || null
      }).returning('id');

      const id = ticketId.id || ticketId;
      const ticket = await this.getTicketById(id);

      // Send email notification to user
      if (ticket && ticket.user_email) {
        try {
          await emailService.sendTicketNotificationEmail(
            ticket.user_email,
            {
              id: ticket.id,
              subject: ticket.title,
              type: ticket.type,
              priority: ticket.priority,
              status: ticket.status,
              description: ticket.description,
              created_at: ticket.created_at
            },
            'created'
          );
        } catch (emailError) {
          console.error('Failed to send ticket creation email:', emailError);
          // Don't throw error - ticket creation should succeed even if email fails
        }
      }

      return ticket;
    } catch (error) {
      console.error('Error creating ticket:', error);
      throw error;
    }
  }

  /**
   * Get ticket by ID with full details
   */
  static async getTicketById(ticketId) {
    try {
      const ticket = await db('tickets as t')
        .select(
          't.*',
          'u.username as user_username',
          'u.email as user_email',
          'assigned.username as assigned_username',
          'g.title as related_game_title',
          'r.title as related_review_title',
          'ru.username as related_user_username'
        )
        .leftJoin('users as u', 't.user_id', 'u.id')
        .leftJoin('users as assigned', 't.assigned_to', 'assigned.id')
        .leftJoin('games as g', 't.related_game_id', 'g.id')
        .leftJoin('reviews as r', 't.related_review_id', 'r.id')
        .leftJoin('users as ru', 't.related_user_id', 'ru.id')
        .where('t.id', ticketId)
        .first();

      if (!ticket) {
        throw new Error('Ticket not found');
      }

      // Get ticket responses
      const responses = await db('ticket_responses as tr')
        .select(
          'tr.*',
          'u.username as user_username',
          'u.role as user_role'
        )
        .join('users as u', 'tr.user_id', 'u.id')
        .where('tr.ticket_id', ticketId)
        .orderBy('tr.created_at', 'asc');

      ticket.responses = responses;
      return ticket;
    } catch (error) {
      console.error('Error getting ticket by ID:', error);
      throw error;
    }
  }

  /**
   * Get tickets with filters and pagination
   */
  static async getTickets(filters = {}, options = {}) {
    try {
      let query = db('tickets as t')
        .select(
          't.*',
          'u.username as user_username',
          'assigned.username as assigned_username',
          'g.title as related_game_title'
        )
        .leftJoin('users as u', 't.user_id', 'u.id')
        .leftJoin('users as assigned', 't.assigned_to', 'assigned.id')
        .leftJoin('games as g', 't.related_game_id', 'g.id');

      // Apply filters
      if (filters.userId) {
        query = query.where('t.user_id', filters.userId);
      }
      if (filters.assignedTo) {
        query = query.where('t.assigned_to', filters.assignedTo);
      }
      if (filters.status) {
        query = query.where('t.status', filters.status);
      }
      if (filters.type) {
        query = query.where('t.type', filters.type);
      }
      if (filters.priority) {
        query = query.where('t.priority', filters.priority);
      }

      // Apply sorting
      const sortBy = options.sortBy || 'created_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.orderBy(`t.${sortBy}`, sortOrder);

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
        if (options.offset) {
          query = query.offset(options.offset);
        }
      }

      return await query;
    } catch (error) {
      console.error('Error getting tickets:', error);
      throw error;
    }
  }

  /**
   * Get tickets count for pagination
   */
  static async getTicketsCount(filters = {}) {
    try {
      let query = db('tickets as t');

      // Apply the same filters as getTickets
      if (filters.userId) {
        query = query.where('t.user_id', filters.userId);
      }
      if (filters.assignedTo) {
        query = query.where('t.assigned_to', filters.assignedTo);
      }
      if (filters.status) {
        query = query.where('t.status', filters.status);
      }
      if (filters.type) {
        query = query.where('t.type', filters.type);
      }
      if (filters.priority) {
        query = query.where('t.priority', filters.priority);
      }

      const result = await query.count('t.id as count').first();
      return parseInt(result.count);
    } catch (error) {
      console.error('Error getting tickets count:', error);
      throw error;
    }
  }

  /**
   * Update ticket
   */
  static async updateTicket(ticketId, updateData) {
    try {
      const updateFields = {};
      const statusChanged = updateData.status !== undefined;

      if (updateData.status) updateFields.status = updateData.status;
      if (updateData.priority) updateFields.priority = updateData.priority;
      if (updateData.assignedTo !== undefined) updateFields.assigned_to = updateData.assignedTo;

      updateFields.updated_at = new Date();

      await db('tickets').where('id', ticketId).update(updateFields);
      const updatedTicket = await this.getTicketById(ticketId);

      // Send email notification if status changed
      if (statusChanged && updatedTicket && updatedTicket.user_email) {
        try {
          await emailService.sendTicketNotificationEmail(
            updatedTicket.user_email,
            {
              id: updatedTicket.id,
              subject: updatedTicket.title,
              status: updatedTicket.status,
              updated_at: updatedTicket.updated_at
            },
            'status_update'
          );
        } catch (emailError) {
          console.error('Failed to send ticket status update email:', emailError);
          // Don't throw error - update should succeed even if email fails
        }
      }

      return updatedTicket;
    } catch (error) {
      console.error('Error updating ticket:', error);
      throw error;
    }
  }

  /**
   * Add response to ticket
   */
  static async addResponse(ticketId, responseData) {
    try {
      const [responseId] = await db('ticket_responses').insert({
        ticket_id: ticketId,
        user_id: responseData.userId,
        message: responseData.message,
        is_internal: responseData.isInternal || false,
        is_system: responseData.isSystem || false
      }).returning('id');

      // Update ticket's updated_at timestamp
      await db('tickets').where('id', ticketId).update({
        updated_at: new Date()
      });

      const id = responseId.id || responseId;

      // Get the response with user info
      const response = await db('ticket_responses as tr')
        .select(
          'tr.*',
          'u.username as user_username',
          'u.role as user_role'
        )
        .join('users as u', 'tr.user_id', 'u.id')
        .where('tr.id', id)
        .first();

      // Send email notification for non-internal, non-system responses
      if (!responseData.isInternal && !responseData.isSystem) {
        try {
          const ticket = await this.getTicketById(ticketId);
          if (ticket && ticket.user_email) {
            await emailService.sendTicketNotificationEmail(
              ticket.user_email,
              {
                id: ticket.id,
                subject: ticket.title,
                status: ticket.status,
                updated_at: ticket.updated_at
              },
              'response'
            );
          }
        } catch (emailError) {
          console.error('Failed to send ticket response email:', emailError);
          // Don't throw error - response creation should succeed even if email fails
        }
      }

      return response;
    } catch (error) {
      console.error('Error adding ticket response:', error);
      throw error;
    }
  }

  /**
   * Assign ticket to moderator/admin
   */
  static async assignTicket(ticketId, assignedTo, assignedBy) {
    try {
      await db('tickets').where('id', ticketId).update({
        assigned_to: assignedTo,
        status: 'in_progress',
        updated_at: new Date()
      });

      // Add system response about assignment
      await this.addResponse(ticketId, {
        userId: assignedBy,
        message: `Ticket assigned to ${assignedTo ? 'staff member' : 'unassigned'}`,
        isSystem: true
      });

      return await this.getTicketById(ticketId);
    } catch (error) {
      console.error('Error assigning ticket:', error);
      throw error;
    }
  }

  /**
   * Close ticket
   */
  static async closeTicket(ticketId, closedBy, reason) {
    try {
      await db('tickets').where('id', ticketId).update({
        status: 'closed',
        updated_at: new Date()
      });

      // Add system response about closure
      await this.addResponse(ticketId, {
        userId: closedBy,
        message: `Ticket closed. Reason: ${reason}`,
        isSystem: true
      });

      return await this.getTicketById(ticketId);
    } catch (error) {
      console.error('Error closing ticket:', error);
      throw error;
    }
  }

  /**
   * Get ticket statistics
   */
  static async getTicketStats(filters = {}) {
    try {
      let baseQuery = db('tickets');

      // Apply date filters if provided
      if (filters.dateFrom) {
        baseQuery = baseQuery.where('created_at', '>=', filters.dateFrom);
      }
      if (filters.dateTo) {
        baseQuery = baseQuery.where('created_at', '<=', filters.dateTo);
      }

      const [
        totalTickets,
        openTickets,
        inProgressTickets,
        resolvedTickets,
        closedTickets,
        ticketsByType,
        ticketsByPriority
      ] = await Promise.all([
        baseQuery.clone().count('id as count').first(),
        baseQuery.clone().where('status', 'open').count('id as count').first(),
        baseQuery.clone().where('status', 'in_progress').count('id as count').first(),
        baseQuery.clone().where('status', 'resolved').count('id as count').first(),
        baseQuery.clone().where('status', 'closed').count('id as count').first(),
        baseQuery.clone().select('type').count('id as count').groupBy('type'),
        baseQuery.clone().select('priority').count('id as count').groupBy('priority')
      ]);

      return {
        total: parseInt(totalTickets.count),
        byStatus: {
          open: parseInt(openTickets.count),
          inProgress: parseInt(inProgressTickets.count),
          resolved: parseInt(resolvedTickets.count),
          closed: parseInt(closedTickets.count)
        },
        byType: ticketsByType.reduce((acc, item) => {
          acc[item.type] = parseInt(item.count);
          return acc;
        }, {}),
        byPriority: ticketsByPriority.reduce((acc, item) => {
          acc[item.priority] = parseInt(item.count);
          return acc;
        }, {})
      };
    } catch (error) {
      console.error('Error getting ticket stats:', error);
      throw error;
    }
  }
}

module.exports = TicketService;

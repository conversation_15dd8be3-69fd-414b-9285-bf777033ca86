const db = require('../config/database');
const TicketService = require('./ticket.service');

/**
 * Report Service
 * Handles all content reporting operations
 */
class ReportService {
  
  /**
   * Create a new report
   */
  static async createReport(reportData) {
    try {
      // Check if user has already reported this content
      const existingReport = await this.checkExistingReport(
        reportData.reporterId,
        reportData.reportedUserId,
        reportData.reportedGameId,
        reportData.reportedReviewId
      );

      if (existingReport) {
        throw new Error('You have already reported this content');
      }

      const [reportId] = await db('reports').insert({
        reporter_id: reportData.reporterId,
        reported_user_id: reportData.reportedUserId || null,
        reported_game_id: reportData.reportedGameId || null,
        reported_review_id: reportData.reportedReviewId || null,
        report_type: reportData.reportType,
        description: reportData.description
      }).returning('id');

      const id = reportId.id || reportId;

      // Automatically create a ticket for the report
      await this.createTicketFromReport(id, reportData);

      return await this.getReportById(id);
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }

  /**
   * Check if user has already reported this content
   */
  static async checkExistingReport(reporterId, reportedUserId, reportedGameId, reportedReviewId) {
    try {
      let query = db('reports').where('reporter_id', reporterId);

      if (reportedUserId) {
        query = query.where('reported_user_id', reportedUserId);
      } else if (reportedGameId) {
        query = query.where('reported_game_id', reportedGameId);
      } else if (reportedReviewId) {
        query = query.where('reported_review_id', reportedReviewId);
      }

      return await query.first();
    } catch (error) {
      console.error('Error checking existing report:', error);
      throw error;
    }
  }

  /**
   * Create a ticket from a report
   */
  static async createTicketFromReport(reportId, reportData) {
    try {
      const report = await this.getReportById(reportId);
      
      let title = 'Content Report: ';
      let relatedGameId = null;
      let relatedReviewId = null;
      let relatedUserId = null;

      if (report.reported_game_title) {
        title += `Game "${report.reported_game_title}"`;
        relatedGameId = report.reported_game_id;
      } else if (report.reported_review_title) {
        title += `Review "${report.reported_review_title}"`;
        relatedReviewId = report.reported_review_id;
      } else if (report.reported_user_username) {
        title += `User "${report.reported_user_username}"`;
        relatedUserId = report.reported_user_id;
      }

      const ticketData = {
        userId: reportData.reporterId,
        title,
        description: `Report Type: ${reportData.reportType}\n\nDescription: ${reportData.description}`,
        type: 'content_report',
        priority: this.getPriorityFromReportType(reportData.reportType),
        relatedGameId,
        relatedReviewId,
        relatedUserId
      };

      const ticket = await TicketService.createTicket(ticketData);
      
      // Link the report to the ticket
      await db('reports').where('id', reportId).update({
        related_ticket_id: ticket.id
      });

      return ticket;
    } catch (error) {
      console.error('Error creating ticket from report:', error);
      throw error;
    }
  }

  /**
   * Get priority level based on report type
   */
  static getPriorityFromReportType(reportType) {
    const highPriorityTypes = ['sexually_explicit', 'harassment', 'copyright_violation'];
    const mediumPriorityTypes = ['offensive_content', 'inappropriate_content', 'rule_violation'];
    
    if (highPriorityTypes.includes(reportType)) {
      return 'high';
    } else if (mediumPriorityTypes.includes(reportType)) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Get report by ID with full details
   */
  static async getReportById(reportId) {
    try {
      const report = await db('reports as r')
        .select(
          'r.*',
          'reporter.username as reporter_username',
          'reported_user.username as reported_user_username',
          'reported_game.title as reported_game_title',
          'reported_review.title as reported_review_title',
          'reviewer.username as reviewer_username'
        )
        .leftJoin('users as reporter', 'r.reporter_id', 'reporter.id')
        .leftJoin('users as reported_user', 'r.reported_user_id', 'reported_user.id')
        .leftJoin('games as reported_game', 'r.reported_game_id', 'reported_game.id')
        .leftJoin('reviews as reported_review', 'r.reported_review_id', 'reported_review.id')
        .leftJoin('users as reviewer', 'r.reviewed_by', 'reviewer.id')
        .where('r.id', reportId)
        .first();

      if (!report) {
        throw new Error('Report not found');
      }

      return report;
    } catch (error) {
      console.error('Error getting report by ID:', error);
      throw error;
    }
  }

  /**
   * Get reports with filters and pagination
   */
  static async getReports(filters = {}, options = {}) {
    try {
      let query = db('reports as r')
        .select(
          'r.*',
          'reporter.username as reporter_username',
          'reported_user.username as reported_user_username',
          'reported_game.title as reported_game_title',
          'reported_review.title as reported_review_title',
          'reviewer.username as reviewer_username'
        )
        .leftJoin('users as reporter', 'r.reporter_id', 'reporter.id')
        .leftJoin('users as reported_user', 'r.reported_user_id', 'reported_user.id')
        .leftJoin('games as reported_game', 'r.reported_game_id', 'reported_game.id')
        .leftJoin('reviews as reported_review', 'r.reported_review_id', 'reported_review.id')
        .leftJoin('users as reviewer', 'r.reviewed_by', 'reviewer.id');

      // Apply filters
      if (filters.reporterId) {
        query = query.where('r.reporter_id', filters.reporterId);
      }
      if (filters.reportType) {
        query = query.where('r.report_type', filters.reportType);
      }
      if (filters.status) {
        query = query.where('r.status', filters.status);
      }
      if (filters.reviewedBy) {
        query = query.where('r.reviewed_by', filters.reviewedBy);
      }

      // Apply sorting
      const sortBy = options.sortBy || 'created_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.orderBy(`r.${sortBy}`, sortOrder);

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
        if (options.offset) {
          query = query.offset(options.offset);
        }
      }

      return await query;
    } catch (error) {
      console.error('Error getting reports:', error);
      throw error;
    }
  }

  /**
   * Get reports count for pagination
   */
  static async getReportsCount(filters = {}) {
    try {
      let query = db('reports as r');

      // Apply the same filters as getReports
      if (filters.reporterId) {
        query = query.where('r.reporter_id', filters.reporterId);
      }
      if (filters.reportType) {
        query = query.where('r.report_type', filters.reportType);
      }
      if (filters.status) {
        query = query.where('r.status', filters.status);
      }
      if (filters.reviewedBy) {
        query = query.where('r.reviewed_by', filters.reviewedBy);
      }

      const result = await query.count('r.id as count').first();
      return parseInt(result.count);
    } catch (error) {
      console.error('Error getting reports count:', error);
      throw error;
    }
  }

  /**
   * Resolve a report
   */
  static async resolveReport(reportId, reviewerId, resolutionNotes, action = null) {
    try {
      await db('reports').where('id', reportId).update({
        status: 'resolved',
        reviewed_by: reviewerId,
        resolution_notes: resolutionNotes,
        resolved_at: new Date()
      });

      // Log the resolution action if provided
      if (action) {
        const ModerationService = require('./moderation.service');
        await ModerationService.logAction(reviewerId, 'resolve_report', resolutionNotes, {
          relatedReportId: reportId,
          actionDetails: action
        });
      }

      return await this.getReportById(reportId);
    } catch (error) {
      console.error('Error resolving report:', error);
      throw error;
    }
  }

  /**
   * Dismiss a report
   */
  static async dismissReport(reportId, reviewerId, reason) {
    try {
      await db('reports').where('id', reportId).update({
        status: 'dismissed',
        reviewed_by: reviewerId,
        resolution_notes: reason,
        resolved_at: new Date()
      });

      // Log the dismissal
      const ModerationService = require('./moderation.service');
      await ModerationService.logAction(reviewerId, 'dismiss_report', reason, {
        relatedReportId: reportId
      });

      return await this.getReportById(reportId);
    } catch (error) {
      console.error('Error dismissing report:', error);
      throw error;
    }
  }

  /**
   * Get report statistics
   */
  static async getReportStats(filters = {}) {
    try {
      let baseQuery = db('reports');

      // Apply date filters if provided
      if (filters.dateFrom) {
        baseQuery = baseQuery.where('created_at', '>=', filters.dateFrom);
      }
      if (filters.dateTo) {
        baseQuery = baseQuery.where('created_at', '<=', filters.dateTo);
      }

      const [
        totalReports,
        pendingReports,
        underReviewReports,
        resolvedReports,
        dismissedReports,
        reportsByType
      ] = await Promise.all([
        baseQuery.clone().count('id as count').first(),
        baseQuery.clone().where('status', 'pending').count('id as count').first(),
        baseQuery.clone().where('status', 'under_review').count('id as count').first(),
        baseQuery.clone().where('status', 'resolved').count('id as count').first(),
        baseQuery.clone().where('status', 'dismissed').count('id as count').first(),
        baseQuery.clone().select('report_type').count('id as count').groupBy('report_type')
      ]);

      return {
        total: parseInt(totalReports.count),
        byStatus: {
          pending: parseInt(pendingReports.count),
          underReview: parseInt(underReviewReports.count),
          resolved: parseInt(resolvedReports.count),
          dismissed: parseInt(dismissedReports.count)
        },
        byType: reportsByType.reduce((acc, item) => {
          acc[item.report_type] = parseInt(item.count);
          return acc;
        }, {})
      };
    } catch (error) {
      console.error('Error getting report stats:', error);
      throw error;
    }
  }
}

module.exports = ReportService;

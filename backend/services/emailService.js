const nodemailer = require('nodemailer');
const logger = require('../config/logger');

/**
 * Email service for sending various types of emails
 */
class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize the email transporter based on environment configuration
   */
  initializeTransporter() {
    try {
      // Check if email configuration is provided
      if (!process.env.EMAIL_SERVICE || !process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
        logger.warn('Email configuration not found. Email service will be disabled.');
        return;
      }

      // Create transporter based on service type
      if (process.env.EMAIL_SERVICE === 'gmail') {
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD // Use app password for Gmail
          }
        });
      } else if (process.env.EMAIL_SERVICE === 'smtp') {
        // Generic SMTP configuration
        this.transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD
          }
        });
      } else {
        logger.error('Unsupported email service:', process.env.EMAIL_SERVICE);
        return;
      }

      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize email service:', error);
    }
  }

  /**
   * Check if email service is available
   */
  isAvailable() {
    return this.transporter !== null;
  }

  /**
   * Send welcome email to new users
   */
  async sendWelcomeEmail(userEmail, username) {
    if (!this.isAvailable()) {
      logger.warn('Email service not available. Skipping welcome email.');
      return { success: false, message: 'Email service not configured' };
    }

    try {
      const mailOptions = {
        from: {
          name: 'IndieRepo Team',
          address: process.env.EMAIL_USER
        },
        to: userEmail,
        subject: 'Welcome to IndieRepo! 🎮',
        html: this.generateWelcomeEmailHTML(username),
        text: this.generateWelcomeEmailText(username)
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info('Welcome email sent successfully:', { 
        to: userEmail, 
        messageId: result.messageId 
      });

      return { 
        success: true, 
        messageId: result.messageId,
        message: 'Welcome email sent successfully'
      };
    } catch (error) {
      logger.error('Failed to send welcome email:', error);
      return { 
        success: false, 
        error: error.message,
        message: 'Failed to send welcome email'
      };
    }
  }

  /**
   * Generate HTML content for welcome email
   */
  generateWelcomeEmailHTML(username) {
    const frontendUrl = process.env.FRONTEND_BASE_URL || process.env.FRONTEND_URL || 'http://localhost:3000';
    
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to IndieRepo</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .logo {
                font-size: 28px;
                font-weight: bold;
                color: #e53e3e;
                margin-bottom: 10px;
            }
            .welcome-text {
                font-size: 18px;
                color: #666;
                margin-bottom: 30px;
            }
            .features {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
            }
            .feature-item {
                margin: 10px 0;
                padding-left: 20px;
                position: relative;
            }
            .feature-item:before {
                content: "🎮";
                position: absolute;
                left: 0;
            }
            .cta-button {
                display: inline-block;
                background: linear-gradient(135deg, #e53e3e, #ff6b35);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">IndieRepo</div>
                <h1>Welcome to the Community, ${username}! 🎉</h1>
            </div>
            
            <div class="welcome-text">
                <p>Thank you for joining IndieRepo, the ultimate destination for discovering and sharing amazing indie games!</p>
                
                <p>We're excited to have you as part of our growing community of indie game enthusiasts, developers, and players.</p>
            </div>

            <div class="features">
                <h3>What you can do now:</h3>
                <div class="feature-item">Discover unique indie games from talented developers</div>
                <div class="feature-item">Upload and share your own games with the community</div>
                <div class="feature-item">Build your profile and track your favorite games</div>
                <div class="feature-item">Connect with other indie game lovers</div>
                <div class="feature-item">Support independent developers</div>
            </div>

            <div style="text-align: center;">
                <a href="${frontendUrl}" class="cta-button">Start Exploring Games</a>
            </div>

            <p>If you have any questions or need help getting started, don't hesitate to reach out to our support team.</p>

            <p>Happy gaming!</p>
            <p><strong>The IndieRepo Team</strong></p>

            <div class="footer">
                <p>This email was sent to you because you created an account on IndieRepo.</p>
                <p>© ${new Date().getFullYear()} IndieRepo. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate plain text content for welcome email
   */
  generateWelcomeEmailText(username) {
    const frontendUrl = process.env.FRONTEND_BASE_URL || process.env.FRONTEND_URL || 'http://localhost:3000';
    
    return `
Welcome to IndieRepo, ${username}!

Thank you for joining IndieRepo, the ultimate destination for discovering and sharing amazing indie games!

We're excited to have you as part of our growing community of indie game enthusiasts, developers, and players.

What you can do now:
• Discover unique indie games from talented developers
• Upload and share your own games with the community  
• Build your profile and track your favorite games
• Connect with other indie game lovers
• Support independent developers

Start exploring: ${frontendUrl}

If you have any questions or need help getting started, don't hesitate to reach out to our support team.

Happy gaming!
The IndieRepo Team

---
This email was sent to you because you created an account on IndieRepo.
© ${new Date().getFullYear()} IndieRepo. All rights reserved.
    `;
  }

  /**
   * Send ticket notification email
   */
  async sendTicketNotificationEmail(userEmail, ticket, type = 'created') {
    if (!this.isAvailable()) {
      logger.warn('Email service not available. Skipping ticket notification email.');
      return { success: false, message: 'Email service not configured' };
    }

    try {
      const frontendUrl = process.env.FRONTEND_BASE_URL || process.env.FRONTEND_URL || 'http://localhost:3000';
      const ticketUrl = `${frontendUrl}/admin/tickets/${ticket.id}`;

      let subject, htmlContent, textContent;

      if (type === 'created') {
        subject = `New Support Ticket #${ticket.id}: ${ticket.subject}`;
        htmlContent = this.generateTicketCreatedEmailHTML(ticket, ticketUrl);
        textContent = this.generateTicketCreatedEmailText(ticket, ticketUrl);
      } else if (type === 'response') {
        subject = `Response to Support Ticket #${ticket.id}: ${ticket.subject}`;
        htmlContent = this.generateTicketResponseEmailHTML(ticket, ticketUrl);
        textContent = this.generateTicketResponseEmailText(ticket, ticketUrl);
      } else if (type === 'status_update') {
        subject = `Ticket #${ticket.id} Status Updated: ${ticket.subject}`;
        htmlContent = this.generateTicketStatusEmailHTML(ticket, ticketUrl);
        textContent = this.generateTicketStatusEmailText(ticket, ticketUrl);
      }

      const mailOptions = {
        from: {
          name: 'IndieRepo Support',
          address: process.env.EMAIL_USER
        },
        to: userEmail,
        subject: subject,
        html: htmlContent,
        text: textContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info('Ticket notification email sent successfully:', {
        to: userEmail,
        ticketId: ticket.id,
        type: type,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Ticket notification email sent successfully'
      };
    } catch (error) {
      logger.error('Failed to send ticket notification email:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to send ticket notification email'
      };
    }
  }

  /**
   * Generate HTML content for ticket created email
   */
  generateTicketCreatedEmailHTML(ticket, ticketUrl) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Support Ticket Created</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e53e3e;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #e53e3e;
                margin-bottom: 10px;
            }
            .ticket-info {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #e53e3e;
            }
            .ticket-field {
                margin: 10px 0;
            }
            .ticket-field strong {
                color: #e53e3e;
            }
            .cta-button {
                display: inline-block;
                background: linear-gradient(135deg, #e53e3e, #ff6b35);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">IndieRepo Support</div>
                <h1>Support Ticket Created</h1>
            </div>

            <p>Thank you for contacting IndieRepo support. We have received your ticket and will respond as soon as possible.</p>

            <div class="ticket-info">
                <div class="ticket-field"><strong>Ticket ID:</strong> #${ticket.id}</div>
                <div class="ticket-field"><strong>Subject:</strong> ${ticket.subject}</div>
                <div class="ticket-field"><strong>Type:</strong> ${ticket.type}</div>
                <div class="ticket-field"><strong>Priority:</strong> ${ticket.priority}</div>
                <div class="ticket-field"><strong>Status:</strong> ${ticket.status}</div>
                <div class="ticket-field"><strong>Created:</strong> ${new Date(ticket.created_at).toLocaleString()}</div>
            </div>

            <div style="text-align: center;">
                <a href="${ticketUrl}" class="cta-button">View Ticket</a>
            </div>

            <p><strong>Your Message:</strong></p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                ${ticket.description.replace(/\n/g, '<br>')}
            </div>

            <p>We typically respond within 24-48 hours. You will receive an email notification when we respond to your ticket.</p>

            <div class="footer">
                <p>© ${new Date().getFullYear()} IndieRepo. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate plain text content for ticket created email
   */
  generateTicketCreatedEmailText(ticket, ticketUrl) {
    return `
Support Ticket Created - IndieRepo

Thank you for contacting IndieRepo support. We have received your ticket and will respond as soon as possible.

Ticket Details:
- Ticket ID: #${ticket.id}
- Subject: ${ticket.subject}
- Type: ${ticket.type}
- Priority: ${ticket.priority}
- Status: ${ticket.status}
- Created: ${new Date(ticket.created_at).toLocaleString()}

Your Message:
${ticket.description}

View your ticket: ${ticketUrl}

We typically respond within 24-48 hours. You will receive an email notification when we respond to your ticket.

© ${new Date().getFullYear()} IndieRepo. All rights reserved.
    `;
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(userEmail, resetToken) {
    if (!this.isAvailable()) {
      logger.warn('Email service not available. Skipping password reset email.');
      return { success: false, message: 'Email service not configured' };
    }

    try {
      const frontendUrl = process.env.FRONTEND_BASE_URL || process.env.FRONTEND_URL || 'http://localhost:3000';
      const resetUrl = `${frontendUrl}/reset-password?token=${resetToken}`;

      const mailOptions = {
        from: {
          name: 'IndieRepo Team',
          address: process.env.EMAIL_USER
        },
        to: userEmail,
        subject: 'Reset Your IndieRepo Password',
        html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Password Reset Request</h2>
          <p>You requested to reset your password for your IndieRepo account.</p>
          <p>Click the button below to reset your password:</p>
          <a href="${resetUrl}" style="display: inline-block; background: #e53e3e; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0;">Reset Password</a>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p>${resetUrl}</p>
          <p>This link will expire in 1 hour for security reasons.</p>
          <p>If you didn't request this password reset, please ignore this email.</p>
          <p>Best regards,<br>The IndieRepo Team</p>
        </div>
        `,
        text: `
Password Reset Request

You requested to reset your password for your IndieRepo account.

Click this link to reset your password: ${resetUrl}

This link will expire in 1 hour for security reasons.

If you didn't request this password reset, please ignore this email.

Best regards,
The IndieRepo Team
        `
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info('Password reset email sent successfully:', {
        to: userEmail,
        messageId: result.messageId
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Password reset email sent successfully'
      };
    } catch (error) {
      logger.error('Failed to send password reset email:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to send password reset email'
      };
    }
  }
  /**
   * Generate HTML content for ticket response email
   */
  generateTicketResponseEmailHTML(ticket, ticketUrl) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ticket Response</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e53e3e;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #e53e3e;
                margin-bottom: 10px;
            }
            .ticket-info {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #28a745;
            }
            .cta-button {
                display: inline-block;
                background: linear-gradient(135deg, #e53e3e, #ff6b35);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">IndieRepo Support</div>
                <h1>New Response to Your Ticket</h1>
            </div>

            <p>We have responded to your support ticket. Please check the details below:</p>

            <div class="ticket-info">
                <div><strong>Ticket ID:</strong> #${ticket.id}</div>
                <div><strong>Subject:</strong> ${ticket.subject}</div>
                <div><strong>Status:</strong> ${ticket.status}</div>
            </div>

            <div style="text-align: center;">
                <a href="${ticketUrl}" class="cta-button">View Full Conversation</a>
            </div>

            <p>If you have any additional questions or concerns, please reply to this ticket.</p>

            <div class="footer">
                <p>© ${new Date().getFullYear()} IndieRepo. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate plain text content for ticket response email
   */
  generateTicketResponseEmailText(ticket, ticketUrl) {
    return `
New Response to Your Ticket - IndieRepo Support

We have responded to your support ticket. Please check the details below:

Ticket ID: #${ticket.id}
Subject: ${ticket.subject}
Status: ${ticket.status}

View full conversation: ${ticketUrl}

If you have any additional questions or concerns, please reply to this ticket.

© ${new Date().getFullYear()} IndieRepo. All rights reserved.
    `;
  }

  /**
   * Generate HTML content for ticket status update email
   */
  generateTicketStatusEmailHTML(ticket, ticketUrl) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ticket Status Update</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .container {
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e53e3e;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #e53e3e;
                margin-bottom: 10px;
            }
            .status-update {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #17a2b8;
            }
            .cta-button {
                display: inline-block;
                background: linear-gradient(135deg, #e53e3e, #ff6b35);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">IndieRepo Support</div>
                <h1>Ticket Status Updated</h1>
            </div>

            <p>The status of your support ticket has been updated:</p>

            <div class="status-update">
                <div><strong>Ticket ID:</strong> #${ticket.id}</div>
                <div><strong>Subject:</strong> ${ticket.subject}</div>
                <div><strong>New Status:</strong> ${ticket.status}</div>
                <div><strong>Updated:</strong> ${new Date(ticket.updated_at).toLocaleString()}</div>
            </div>

            <div style="text-align: center;">
                <a href="${ticketUrl}" class="cta-button">View Ticket</a>
            </div>

            <div class="footer">
                <p>© ${new Date().getFullYear()} IndieRepo. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Generate plain text content for ticket status update email
   */
  generateTicketStatusEmailText(ticket, ticketUrl) {
    return `
Ticket Status Updated - IndieRepo Support

The status of your support ticket has been updated:

Ticket ID: #${ticket.id}
Subject: ${ticket.subject}
New Status: ${ticket.status}
Updated: ${new Date(ticket.updated_at).toLocaleString()}

View ticket: ${ticketUrl}

© ${new Date().getFullYear()} IndieRepo. All rights reserved.
    `;
  }
}

// Create and export a singleton instance
const emailService = new EmailService();
module.exports = emailService;

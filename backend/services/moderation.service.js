const db = require('../config/database');

/**
 * Moderation Service
 * Handles all moderation-related operations
 */
class ModerationService {
  
  /**
   * Log a moderation action
   */
  static async logAction(moderatorId, actionType, reason, options = {}) {
    try {
      const actionData = {
        moderator_id: moderatorId,
        action_type: actionType,
        reason: reason,
        target_user_id: options.targetUserId || null,
        target_game_id: options.targetGameId || null,
        target_review_id: options.targetReviewId || null,
        related_report_id: options.relatedReportId || null,
        action_details: options.actionDetails ? JSON.stringify(options.actionDetails) : null,
        expires_at: options.expiresAt || null
      };

      const [actionId] = await db('moderation_actions').insert(actionData).returning('id');
      return actionId.id || actionId;
    } catch (error) {
      console.error('Error logging moderation action:', error);
      throw error;
    }
  }

  /**
   * Ban a user
   */
  static async banUser(moderatorId, targetUserId, reason, duration = null) {
    try {
      const banData = {
        is_banned: true,
        ban_reason: reason,
        banned_until: duration ? new Date(Date.now() + duration * 1000) : null
      };

      await db('users').where('id', targetUserId).update(banData);

      // Log the action
      await this.logAction(moderatorId, 'ban_user', reason, {
        targetUserId,
        expiresAt: banData.banned_until,
        actionDetails: { duration }
      });

      return { success: true, message: 'User banned successfully' };
    } catch (error) {
      console.error('Error banning user:', error);
      throw error;
    }
  }

  /**
   * Unban a user
   */
  static async unbanUser(moderatorId, targetUserId, reason) {
    try {
      await db('users').where('id', targetUserId).update({
        is_banned: false,
        banned_until: null,
        ban_reason: null
      });

      // Log the action
      await this.logAction(moderatorId, 'unban_user', reason, {
        targetUserId
      });

      return { success: true, message: 'User unbanned successfully' };
    } catch (error) {
      console.error('Error unbanning user:', error);
      throw error;
    }
  }

  /**
   * Delete a game
   */
  static async deleteGame(moderatorId, gameId, reason) {
    try {
      // Soft delete by updating status
      await db('games').where('id', gameId).update({
        status: 'removed'
      });

      // Log the action
      await this.logAction(moderatorId, 'delete_game', reason, {
        targetGameId: gameId
      });

      return { success: true, message: 'Game deleted successfully' };
    } catch (error) {
      console.error('Error deleting game:', error);
      throw error;
    }
  }

  /**
   * Restore a game
   */
  static async restoreGame(moderatorId, gameId, reason) {
    try {
      await db('games').where('id', gameId).update({
        status: 'published'
      });

      // Log the action
      await this.logAction(moderatorId, 'restore_game', reason, {
        targetGameId: gameId
      });

      return { success: true, message: 'Game restored successfully' };
    } catch (error) {
      console.error('Error restoring game:', error);
      throw error;
    }
  }

  /**
   * Delete a review
   */
  static async deleteReview(moderatorId, reviewId, reason) {
    try {
      await db('reviews').where('id', reviewId).del();

      // Log the action
      await this.logAction(moderatorId, 'delete_review', reason, {
        targetReviewId: reviewId
      });

      return { success: true, message: 'Review deleted successfully' };
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }

  /**
   * Edit a review
   */
  static async editReview(moderatorId, reviewId, newContent, reason) {
    try {
      const originalReview = await db('reviews').where('id', reviewId).first();
      
      await db('reviews').where('id', reviewId).update({
        comment: newContent.comment || originalReview.comment,
        title: newContent.title || originalReview.title,
        rating: newContent.rating || originalReview.rating
      });

      // Log the action
      await this.logAction(moderatorId, 'edit_review', reason, {
        targetReviewId: reviewId,
        actionDetails: {
          originalContent: {
            comment: originalReview.comment,
            title: originalReview.title,
            rating: originalReview.rating
          },
          newContent
        }
      });

      return { success: true, message: 'Review edited successfully' };
    } catch (error) {
      console.error('Error editing review:', error);
      throw error;
    }
  }

  /**
   * Promote user role
   */
  static async promoteUser(adminId, targetUserId, newRole, reason) {
    try {
      const originalUser = await db('users').select('role').where('id', targetUserId).first();
      
      await db('users').where('id', targetUserId).update({
        role: newRole
      });

      // Log the action
      await this.logAction(adminId, 'promote_user', reason, {
        targetUserId,
        actionDetails: {
          originalRole: originalUser.role,
          newRole
        }
      });

      return { success: true, message: 'User role updated successfully' };
    } catch (error) {
      console.error('Error promoting user:', error);
      throw error;
    }
  }

  /**
   * Get moderation logs with filters
   */
  static async getModerationLogs(filters = {}, options = {}) {
    try {
      let query = db('moderation_actions as ma')
        .select(
          'ma.*',
          'moderator.username as moderator_username',
          'target_user.username as target_username',
          'target_game.title as target_game_title'
        )
        .leftJoin('users as moderator', 'ma.moderator_id', 'moderator.id')
        .leftJoin('users as target_user', 'ma.target_user_id', 'target_user.id')
        .leftJoin('games as target_game', 'ma.target_game_id', 'target_game.id');

      // Apply filters
      if (filters.moderatorId) {
        query = query.where('ma.moderator_id', filters.moderatorId);
      }
      if (filters.actionType) {
        query = query.where('ma.action_type', filters.actionType);
      }
      if (filters.targetUserId) {
        query = query.where('ma.target_user_id', filters.targetUserId);
      }
      if (filters.dateFrom) {
        query = query.where('ma.created_at', '>=', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.where('ma.created_at', '<=', filters.dateTo);
      }

      // Apply sorting
      query = query.orderBy('ma.created_at', 'desc');

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
        if (options.offset) {
          query = query.offset(options.offset);
        }
      }

      return await query;
    } catch (error) {
      console.error('Error getting moderation logs:', error);
      throw error;
    }
  }

  /**
   * Get moderation logs count for pagination
   */
  static async getModerationLogsCount(filters = {}) {
    try {
      let query = db('moderation_actions as ma');

      // Apply the same filters as getModerationLogs
      if (filters.moderatorId) {
        query = query.where('ma.moderator_id', filters.moderatorId);
      }
      if (filters.actionType) {
        query = query.where('ma.action_type', filters.actionType);
      }
      if (filters.targetUserId) {
        query = query.where('ma.target_user_id', filters.targetUserId);
      }
      if (filters.dateFrom) {
        query = query.where('ma.created_at', '>=', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.where('ma.created_at', '<=', filters.dateTo);
      }

      const result = await query.count('* as count').first();
      return parseInt(result.count);
    } catch (error) {
      console.error('Error getting moderation logs count:', error);
      throw error;
    }
  }

  /**
   * Get user ban status
   */
  static async getUserBanStatus(userId) {
    try {
      const user = await db('users')
        .select('is_banned', 'banned_until', 'ban_reason')
        .where('id', userId)
        .first();

      if (!user) {
        throw new Error('User not found');
      }

      return {
        isBanned: user.is_banned,
        bannedUntil: user.banned_until,
        banReason: user.ban_reason,
        isPermanent: user.is_banned && !user.banned_until
      };
    } catch (error) {
      console.error('Error getting user ban status:', error);
      throw error;
    }
  }
}

module.exports = ModerationService;

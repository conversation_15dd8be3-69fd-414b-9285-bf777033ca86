# IndieRepo Backend Scripts

This directory contains utility scripts for managing the IndieRepo application.

## Admin User Management

### Default Admin User

The application automatically creates a default admin user during deployment with the following credentials:

- **Email**: `<EMAIL>`
- **Username**: `indierepo_admin`
- **Role**: `admin`
- **Password**: `IndieRepo2024!Admin` (default, can be customized)

### Creating Admin User

#### Automatic Creation (Deployment)
The admin user is automatically created during deployment via the CI/CD pipeline.

#### Manual Creation
You can manually create or update the admin user using:

```bash
# Development environment
npm run create-admin

# Production environment
npm run create-admin:prod
```

#### Custom Password
To use a custom password for the admin user, set the environment variable:

```bash
export ADMIN_DEFAULT_PASSWORD="your-custom-password"
npm run create-admin:prod
```

### Security Notes

1. **Change Default Password**: After deployment, log in with the default credentials and change the password through the admin interface.

2. **Environment Variables**: In production, the password is hidden in logs for security. Use the `ADMIN_DEFAULT_PASSWORD` environment variable to set a custom password.

3. **Admin Access**: The admin user has full access to:
   - User management
   - Game moderation
   - Review management
   - System settings
   - Reports and analytics

### Troubleshooting

If the admin user creation fails:

1. Check database connectivity
2. Verify the users table exists (run migrations first)
3. Check for any database permission issues
4. Review the application logs for specific error messages

The script is idempotent - it can be run multiple times safely. If the user already exists, it will update the role and password.

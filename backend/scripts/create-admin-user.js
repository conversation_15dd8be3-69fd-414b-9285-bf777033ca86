const db = require('../config/database');
const bcrypt = require('bcryptjs');

async function createAdminUser() {
  try {
    console.log('🔍 Creating default admin user...');

    const email = '<EMAIL>';
    const username = 'indierepo_admin';
    // Use environment variable for password if available, otherwise use default
    const password = process.env.ADMIN_DEFAULT_PASSWORD || 'IndieRepo2024!Admin';
    
    // Check if user already exists
    const existingUser = await db('users').where('email', email).first();
    if (existingUser) {
      console.log('⚠️ Default admin user already exists, ensuring admin role...');

      // Hash the new password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Update the user to ensure admin role and password
      await db('users')
        .where('email', email)
        .update({
          password: hashedPassword,
          role: 'admin',
          is_verified: true
        });

      console.log('✅ Default admin user role and credentials updated');
    } else {
      // Hash the password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create new admin user
      await db('users').insert({
        username: username,
        email: email,
        password: hashedPassword,
        role: 'admin',
        is_verified: true,
        provider: 'local'
      });

      console.log('✅ Default admin user created successfully');
    }
    
    console.log(`\n📧 Default Admin Email: ${email}`);
    console.log(`👤 Username: ${username}`);
    console.log(`🛡️ Role: admin`);
    console.log(`✅ Default admin user is ready for deployment`);

    // Only show password in development mode for security
    if (process.env.NODE_ENV !== 'production') {
      console.log(`🔑 Password: ${password}`);
    } else {
      console.log(`🔑 Password: [Hidden in production - use environment variable ADMIN_DEFAULT_PASSWORD if custom password needed]`);
    }

  } catch (error) {
    console.error('❌ Failed to create default admin user:', error);
  } finally {
    await db.destroy();
    process.exit(0);
  }
}

createAdminUser();

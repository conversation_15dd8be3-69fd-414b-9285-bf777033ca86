const db = require('../config/database');

async function checkDatabase() {
  try {
    console.log('🔍 Checking database...');
    
    // Check users
    const users = await db('users').select('id', 'username', 'email', 'role').limit(5);
    console.log('\n👥 Users:');
    console.table(users);
    
    // Check reviews
    const reviews = await db('reviews').select('id', 'user_id', 'game_id', 'comment', 'rating').limit(5);
    console.log('\n⭐ Reviews:');
    console.table(reviews);
    
    // Check games
    const games = await db('games').select('id', 'title', 'user_id').limit(5);
    console.log('\n🎮 Games:');
    console.table(games);
    
    console.log('\n✅ Database check complete');
    
  } catch (error) {
    console.error('❌ Database check failed:', error);
  } finally {
    await db.destroy();
    process.exit(0);
  }
}

checkDatabase();

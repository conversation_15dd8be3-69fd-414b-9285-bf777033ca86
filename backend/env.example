# Database Configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=indierepo

# Server Configuration
PORT=5000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# AWS S3 Configuration (Required for file uploads)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=your-s3-bucket-name

# Optional: AWS S3 Additional Configuration
# AWS_S3_ENDPOINT=https://s3.us-east-1.amazonaws.com (for custom endpoints)
# AWS_S3_FORCE_PATH_STYLE=false (for MinIO or custom S3-compatible services)

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Email Configuration (Required for welcome emails and password reset)
# For Gmail: Use 'gmail' service and app password (not regular password)
# For other SMTP: Use 'smtp' service and configure SMTP_* variables below
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password_here

# SMTP Configuration (only needed if EMAIL_SERVICE=smtp)
# SMTP_HOST=smtp.your-provider.com
# SMTP_PORT=587
# SMTP_SECURE=false

# Payment Configuration (Optional - for game monetization)
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# Discord OAuth Configuration (Optional)
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret

# Application URLs
BASE_URL=http://localhost:5000
FRONTEND_BASE_URL=http://localhost:3000 